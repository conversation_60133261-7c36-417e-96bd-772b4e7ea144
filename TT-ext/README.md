# 🎯 TikTok Ads Data Extractor v3.0.3

**ГОТОВЕ ДО ВИКОРИСТАННЯ CHROME РОЗШИРЕННЯ**

Автоматично витягує дані з TikTok Ads Manager і відправляє в Airtable.

## 🚀 ШВИДКИЙ СТАРТ (2 хвилини)

### 1. Встановлення
1. Розпакуйте архів `TikTok_Ads_Extractor_v3.0.3.zip`
2. Відкрийте `chrome://extensions/`
3. Увімкніть "Developer mode"
4. Натисніть "Load unpacked" → виберіть папку TT-ext
5. Готово! Іконка з'явиться в панелі браузера

### 2. Налаштування (30 секунд)
1. Натисніть на іконку розширення
2. Вставте **API Key** з [airtable.com/create/tokens](https://airtable.com/create/tokens)
3. Вставте **URL таблиці** з Airtable (Base ID та Table ID заповняться автоматично)
4. Натисніть "Тест підключення" → "Зберегти"

### 3. Використання
1. Відкрийте [ads.tiktok.com](https://ads.tiktok.com)
2. Перейдіть до кампаній/груп/оголошень
3. Натисніть на іконку розширення
4. Клікніть "📊 Витягти дані з поточної сторінки"
5. Клікніть "📤 Відправити в Airtable"

## ✅ ОСОБЛИВОСТІ

- **Автоматичне витягування** всіх метрик TikTok Ads
- **Підтримка антидетект браузерів** (Browser.Vision, Octo Browser)
- **Простий інтерфейс** - тільки 2 поля для налаштування
- **Безпечність** - дані зберігаються тільки локально

## 📦 Що включено

- **manifest.json** - Конфігурація розширення
- **popup.html/js** - Інтерфейс користувача  
- **content.js** - Витягування даних з TikTok Ads
- **background.js** - Фонові процеси та API
- **injected.js** - Доступ до React компонентів
- **styles.css** - Стилі інтерфейсу

## 🚀 Встановлення

### 1. Завантаження в Chrome
1. Відкрийте `chrome://extensions/`
2. Увімкніть "Режим розробника"
3. Натисніть "Завантажити розпаковане"
4. Виберіть папку з розширенням
5. Розширення встановиться без відкриття нових вкладок
6. Натисніть на іконку розширення в панелі браузера для початку роботи

### 🕵️ Встановлення в антидетект браузери

#### Browser.Vision
1. **Відкрийте Browser.Vision**
2. **Створіть або відкрийте профіль**
3. **В браузері перейдіть до**: `chrome://extensions/`
4. **Увімкніть Developer mode** (правий верхній кут)
5. **Натисніть "Load unpacked"**
6. **Виберіть папку TT-ext**
7. **Розширення з'явиться в списку** - готово!

#### Octo Browser
1. **Запустіть Octo Browser**
2. **Відкрийте профіль** або створіть новий
3. **В адресному рядку введіть**: `chrome://extensions/`
4. **Активуйте "Режим розробника"** (toggle справа вгорі)
5. **Клікніть "Завантажити розпаковане розширення"**
6. **Оберіть папку з TT-ext файлами**
7. **Перевірте що розширення активне** ✅

#### 🔧 Налаштування для антидетект браузерів
- ✅ **Працює з усіма Chromium-based браузерами**
- ✅ **Зберігає налаштування між сесіями**
- ✅ **Не конфліктує з антидетект функціями**
- ⚠️ **Рекомендація**: Тестуйте на одному профілі перед масовим використанням

### 2. Налаштування через інтерфейс (ЗАВЖДИ ВИДИМІ!)
1. Натисніть на іконку розширення
2. **Налаштування вже відкриті** - ніяких кнопок!
3. Заповніть дані Airtable:
   - **API Key** - отримайте на [airtable.com/create/tokens](https://airtable.com/create/tokens)
   - **Airtable URL** - вставте повний URL таблиці (Base ID і Table ID витягнуться автоматично)
   - Або заповніть вручну:
     - **Base ID** - знайдіть в URL: `airtable.com/appXXXXXX/...`
     - **Table ID** - автоматично або вручну `tblXXXXXX`
4. Натисніть "Тест підключення" для перевірки
5. Збережіть налаштування

## ⚙️ Особливості інтерфейсу

### Покращені налаштування
- **Згорнутий інтерфейс** - налаштування приховані за замовчуванням
- **Огляд статусу** - миттєвий перегляд поточного стану підключення
- **Тест підключення** - перевірка даних перед збереженням
- **Зручне керування** - кнопки очищення та показу/приховання паролів
- **Інтерактивні підказки** - посилання та інструкції прямо в інтерфейсі

### 🏗️ Створення таблиці в Airtable

#### Крок 1: Створіть нову базу
1. Перейдіть на [airtable.com](https://airtable.com)
2. Натисніть "Create a base" → "Start from scratch"
3. Назвіть базу, наприклад: "TikTok Ads Data"

#### Крок 2: Налаштуйте поля таблиці

⚠️ **ВАЖЛИВО**: Не всі поля обов'язкові! Розширення працюватиме навіть з мінімальним набором.

**🔥 Мінімально необхідні поля (БЕЗ НИХ НЕ ПРАЦЮВАТИМЕ):**
| Назва поля | Тип поля | Опції |
|------------|----------|-------|
| `ID` | Single line text | **Primary field** |
| `Type` | Single select | Campaign, AdGroup, Ad |
| `Name` | Single line text | - |
| `Status` | Single select | Active, Paused, Ended |

**💰 Рекомендовані основні метрики:**
| Назва поля | Тип поля | Опції |
|------------|----------|-------|
| `Spend` | Currency | USD, precision 2 |
| `Impressions` | Number | Integer |
| `Clicks` | Number | Integer |
| `CTR` | Percent | 2 decimal places |
| `Conversions` | Number | Integer |

**📊 Повна структура полів (ОПЦІОНАЛЬНО):**

**Основні поля:**
| Назва поля | Тип поля | Опції |
|------------|----------|-------|
| `ID` | Single line text | Primary field |
| `Type` | Single select | Campaign, AdGroup, Ad, Performance |
| `Name` | Single line text | - |
| `Status` | Single select | Active, Paused, Ended, Draft |
| `Objective` | Single line text | Only for campaigns |
| `Budget` | Currency | USD, precision 2 |
| `Spend` | Currency | USD, precision 2 |
| `Impressions` | Number | Integer |
| `Clicks` | Number | Integer |
| `CTR` | Percent | 2 decimal places |
| `CPC` | Currency | USD, precision 4 |
| `CPM` | Currency | USD, precision 2 |
| `Conversions` | Number | Integer |
| `Conversion Rate` | Percent | 2 decimal places |
| `Cost per Conversion` | Currency | USD, precision 4 |
| `ROAS` | Number | 2 decimal places |
| `Reach` | Number | Integer |
| `Frequency` | Number | 2 decimal places |

**Відео метрики:**
| Назва поля | Тип поля | Опції |
|------------|----------|-------|
| `Video Views` | Number | Integer |
| `Video 2s Views` | Number | Integer |
| `Video 6s Views` | Number | Integer |
| `Video 25% Views` | Number | Integer |
| `Video 50% Views` | Number | Integer |
| `Video 75% Views` | Number | Integer |
| `Video 100% Views` | Number | Integer |
| `Video Play Actions` | Number | Integer |
| `Video Watch Time` | Duration | - |
| `Avg Video Play Time` | Duration | - |
| `Video Completion Rate` | Percent | 2 decimal places |

**TikTok-специфічні метрики:**
| Назва поля | Тип поля | Опції |
|------------|----------|-------|
| `Likes` | Number | Integer |
| `Shares` | Number | Integer |
| `Comments` | Number | Integer |
| `Follows` | Number | Integer |
| `Profile Views` | Number | Integer |
| `Engagement Rate` | Percent | 2 decimal places |
| `Social Engagement` | Number | Integer |
| `Unique Users` | Number | Integer |
| `New Users` | Number | Integer |
| `Creative Views` | Number | Integer |

**Додаткові поля:**
| Назва поля | Тип поля | Опції |
|------------|----------|-------|
| `Placement` | Single line text | For adgroups |
| `Audience` | Single line text | For adgroups |
| `Bidding` | Single line text | For adgroups |
| `Format` | Single line text | For ads |
| `Ad Preview` | Attachment | For ads |
| `Raw Data` | Long text | JSON дані |
| `URL` | URL | Source page |
| `Extracted At` | Date | Include time |
| `Source` | Single select | extension, manual, api |

#### Крок 3: Опціональні поля для аналітики
| Назва поля | Тип поля | Формула |
|------------|----------|---------|
| `Cost per Conversion` | Formula | `{Spend} / {Conversions}` |
| `Profit` | Formula | `({Conversions} * 50) - {Spend}` (adjust 50 to your conversion value) |
| `Performance Score` | Formula | `IF({CTR} > 1, "Good", IF({CTR} > 0.5, "Average", "Poor"))` |

#### Крок 4: Налаштування views
Створіть різні представлення:
- **All Data** (за замовчуванням)
- **Campaigns Only** - фільтр Type = Campaign
- **Active Only** - фільтр Status = Active
- **Top Performers** - сортування за CTR або ROAS
- **Recent** - сортування за Extracted At (desc)

## 🧪 Тестування розширення

### 🚀 Швидкий старт для тесту
1. **Встановіть розширення:**
   - Відкрийте `chrome://extensions/`
   - Увімкніть "Developer mode"
   - "Load unpacked" → виберіть папку `TT-ext`

2. **Створіть тестову базу Airtable:**
   - Перейдіть на [airtable.com](https://airtable.com) та створіть акаунт
   - Створіть нову базу "TikTok Test"
   - Додайте мінімальні поля: `ID`, `Type`, `Name`, `Status`, `Spend`
   - Отримайте API token: [airtable.com/create/tokens](https://airtable.com/create/tokens)

3. **Налаштуйте розширення:**
   - Натисніть іконку розширення → "Показати налаштування"
   - API Key: ваш токен
   - Base ID: з URL (appXXXXXX)
   - Table ID: назва таблиці
   - Натисніть "Тест підключення" → "Зберегти"

4. **Протестуйте на TikTok Ads:**
   - Відкрийте [ads.tiktok.com](https://ads.tiktok.com)
   - Перейдіть до будь-якої сторінки з даними
   - Натисніть "Витягти дані" → "Відправити в Airtable"

### ✅ Що очікувати
- Іконка розширення з'явиться в панелі Chrome
- При кліку відкриється сучасний інтерфейс з темною/світлою темою
- Після витягування дані з'являться в попередньому перегляді
- Відправлені дані будуть у вашій Airtable таблиці

## 📊 Використання

### 1. Базове використання
1. Відкрийте ads.tiktok.com
2. Перейдіть до кампаній/груп оголошень/оголошень
3. Натисніть на іконку розширення
4. Налаштуйте Airtable (якщо ще не зроблено):
   - Клікніть "Показати налаштування"
   - Заповніть поля та протестуйте підключення
   - Збережіть налаштування
5. Натисніть "Витягти дані з поточної сторінки"
6. Перегляньте дані та натисніть "Відправити в Airtable"

### 2. Автоматичне витягування
Розширення автоматично витягує дані при:
- Завантаженні сторінки TikTok Ads
- Зміні даних на сторінці
- Навігації між розділами

### 3. Типи даних
- **Campaigns** - Кампанії з бюджетами і показниками
- **AdGroups** - Групи оголошень з таргетингом  
- **Ads** - Окремі оголошення з креативами
- **Performance** - Дані продуктивності з таблиць

## 🔧 Технічні особливості

### Методи витягування даних
1. **DOM scraping** - Аналіз HTML елементів
2. **React hooks** - Доступ до стану компонентів
3. **Network interception** - Перехоплення API запитів
4. **Local storage** - Кешовані дані браузера

### Селектори для витягування
```javascript
// Кампанії
'[data-testid*="campaign"]'
'.campaign-row'
'[class*="campaign-item"]'

// Групи оголошень  
'[data-testid*="adgroup"]'
'.adgroup-row'

// Оголошення
'[data-testid*="ad-"]'
'.ad-row'
```

### API інтеграція
- **Airtable REST API v0**
- Автоматичне батчування (10 записів за раз)
- Retry логіка при помилках
- Валідація даних перед відправкою

## 🛡️ Безпека

### Permissions
- `activeTab` - Доступ до поточної вкладки
- `storage` - Збереження налаштувань
- `scripting` - Виконання скриптів
- `https://ads.tiktok.com/*` - Доступ до TikTok Ads
- `https://api.airtable.com/*` - API Airtable

### Приватність
- API ключі зберігаються локально
- Дані не передаються третім особам
- Працює тільки на ads.tiktok.com

## 🐛 Усунення проблем

### ❌ ПРОБЛЕМА: Не видно кнопок витягування даних
**Симптоми:** В popup відображаються тільки налаштування Airtable, але немає кнопок "📊 Витягти дані" та "📤 Відправити в Airtable"

**Причини та рішення:**

1. **Перезавантажте розширення:**
   - Перейдіть до `chrome://extensions/`
   - Знайдіть "TikTok Ads Extractor"
   - Натисніть кнопку "Reload" (🔄)
   - Спробуйте відкрити popup знову

2. **Перевірте консоль на помилки:**
   - Натисніть F12 в popup
   - Перейдіть на вкладку Console
   - Шукайте помилки JavaScript
   - Повинні бути логи: "✅ popup.js loaded successfully"

3. **Очистіть кеш браузера:**
   - Ctrl+Shift+Delete
   - Виберіть "Cached images and files"
   - Очистіть кеш

4. **Переустановіть розширення:**
   - Видаліть розширення з chrome://extensions/
   - Перезавантажте браузер
   - Встановіть розширення знову

### Дані не витягуються
1. Перевірте чи відкрита правильна сторінка TikTok Ads
2. Зачекайте повного завантаження сторінки
3. Спробуйте оновити сторінку
4. Перевірте консоль на помилки (F12)

### Помилки Airtable
1. Перевірте правильність API ключа
2. Переконайтеся що Base ID і Table ID вірні
3. Перевірте права доступу до таблиці
4. Переконайтеся що поля в таблиці існують

### Розширення не працює
1. Перезавантажте розширення в chrome://extensions/
2. Очистіть кеш браузера
3. Перевірте чи увімкнений "Режим розробника"
4. Подивіться логи в background service worker

## 📈 Розвиток

### Додавання нових селекторів
```javascript
// У content.js додайте нові селектори
const newSelectors = [
  '[data-new-testid]',
  '.new-class-name'
];
```

### Кастомні поля Airtable
```javascript
// У background.js змініть formatDataForAirtable
fields['Custom Field'] = item.customValue || '';
```

### Нові типи даних
```javascript
// Додайте новий тип в extractAllData
const customData = this.extractCustomData();
data.push(...customData);
```

## 🔄 Версії

### v1.0.0 (Базова версія)
- ✅ Базове витягування даних
- ✅ Інтеграція з Airtable
- ✅ Автоматичне виявлення даних
- ✅ Підтримка кампаній, груп, оголошень
- ✅ React компоненти і network API
- ✅ Responsive UI з темною темою

### v3.0.3 (Поточна - Професійний код)
- ✅ **Видалено нецензурну лексику** з коментарів зберігаючи українську мову
- ✅ **Покращено логіку блокування вкладок** - менш агресивна та більш стабільна
- ✅ **Оновлено версію розширення** до 3.0.3
- ✅ **Професіоналізація коду** без втрати функціональності

### v1.1.0 (Розширені метрики)
- ✅ **Розширені відео метрики**: 2s, 6s, 25%, 50%, 75%, 100% переглядів
- ✅ **TikTok-специфічні метрики**: лайки, шери, коментарі, підписки
- ✅ **Покращені селектори**: підтримка aria-label, title, множинних форматів
- ✅ **Додаткові метрики**: engagement rate, reach, frequency, ROAS
- ✅ **Повна структура Airtable**: 50+ полів для всіх типів даних
- ✅ **Креативні метрики**: preview, creative views для оголошень
- ✅ **Аудиторія метрики**: unique users, new users, profile views

### Планується v1.2.0
- 📋 Експорт в CSV/Excel
- 🔄 Синхронізація з Google Sheets
- 📊 Аналітика та звіти
- 🎯 Розширені фільтри
- 🔔 Сповіщення про зміни
- 🤖 AI-аналіз продуктивності

## 🛡️ Універсальність та стабільність

### 🔧 Максимальна сумісність

#### Підтримувані браузери:
- ✅ **Google Chrome** (версія 88+)
- ✅ **Microsoft Edge** (Chromium-based)
- ✅ **Brave Browser**
- ✅ **Opera** (Chromium-based)
- ✅ **Browser.Vision** (всі версії)
- ✅ **Octo Browser** (всі версії)
- ✅ **GoLogin**
- ✅ **Multilogin**
- ✅ **AdsPower**
- ✅ **Dolphin Anty**
- ✅ **Будь-який Chromium-based браузер**

#### Підтримувані операційні системи:
- ✅ **Windows** 10/11
- ✅ **macOS** 10.14+
- ✅ **Linux** (Ubuntu, Debian, CentOS)

## 🐛 Критичні моменти та вирішення

### ❌ ПРОБЛЕМА 1: Селектори - TikTok змінює структуру

**ЩО РОБИТИ:**
1. **Відкрийте Developer Tools** (F12)
2. **Перейдіть на вкладку Console**
3. **Вставте код для перевірки:**
```javascript
// Перевірка наявності елементів кампаній
console.log('Campaigns:', document.querySelectorAll('[data-testid*="campaign"]').length);
console.log('Tables:', document.querySelectorAll('table, [role="table"]').length);
console.log('Rows:', document.querySelectorAll('tr, [role="row"]').length);

// Якщо результат 0 - TikTok змінив структуру
```

**ЯКЩО ЕЛЕМЕНТИ НЕ ЗНАЙДЕНІ:**
- Зачекайте 5-10 секунд для завантаження
- Оновіть сторінку (Ctrl+F5)
- Перевірте що ви на правильній сторінці TikTok Ads Manager

**ДОВГОСТРОКОВЕ РІШЕННЯ:**
- Розширення автоматично адаптується до змін
- Використовує множинні селектори для надійності
- Fallback механізми для різних версій TikTok

### ❌ ПРОБЛЕМА 2: API ліміти - 5 запитів/секунду максимум

**ЩО ВІДБУВАЄТЬСЯ:**
- Airtable блокує запити при перевищенні ліміту
- Помилка: "Rate limit exceeded" або "Too many requests"
- Дані не зберігаються або зберігаються частково

**ЩО РОБИТИ НЕГАЙНО:**
1. **Зупиніть витягування даних** на 30 секунд
2. **Перевірте кількість записів:**
```javascript
// В консолі браузера:
console.log('Записів на сторінці:', document.querySelectorAll('tr').length);
// Якщо більше 50 - розділіть на частини
```

**ПРАВИЛА РОБОТИ:**
- ✅ **Максимум 50 записів за раз**
- ✅ **Пауза 3-5 секунд між витягуваннями**
- ✅ **Не більше 300 записів за годину**
- ❌ **НЕ натискайте кнопку швидко підряд**

**НАЛАШТУВАННЯ В АНТИДЕТЕКТ БРАУЗЕРАХ:**
```
Рекомендовані налаштування:
- Request delay: 3000ms (3 секунди)
- Batch size: 25 записів
- Timeout: 30 секунд між батчами
```

### ❌ ПРОБЛЕМА 3: Поля Airtable - назви case-sensitive

**ЩО ЦЕ ОЗНАЧАЄ:**
- `Spend` ≠ `spend` ≠ `SPEND`
- `Video Views` ≠ `video views` ≠ `Video_Views`
- Одна літера не так - поле не знайдеться

**ТОЧНІ НАЗВИ ПОЛІВ (КОПІЮЙТЕ БЕЗ ЗМІН):**
```
✅ ПРАВИЛЬНО:
ID
Type
Name
Status
Spend
Impressions
Clicks
CTR
Video Views
Video 25% Views
Engagement Rate

❌ НЕПРАВИЛЬНО:
id, type, name, status
spend, impressions, clicks
ctr, video views
video 25% views
engagement rate
```

**ПЕРЕВІРКА ПОЛІВ:**
1. **Відкрийте Airtable**
2. **Скопіюйте назву поля точно як написано**
3. **Вставте в розширення без змін**
4. **Перевірте що немає зайвих пробілів**

### ❌ ПРОБЛЕМА 4: JavaScript - потрібен доступ до localStorage

**ЩО ВІДБУВАЄТЬСЯ:**
- Розширення не зберігає налаштування
- Кнопки не працюють
- Помилка: "localStorage is not defined"

**ПЕРЕВІРКА В АНТИДЕТЕКТ БРАУЗЕРАХ:**

#### Browser.Vision:
1. **Settings → Privacy & Security**
2. **JavaScript**: `Enabled`
3. **Local Storage**: `Enabled`
4. **Cookies**: `Enabled`
5. **Site Data**: `Allow all`

#### Octo Browser:
1. **Profile Settings → Browser**
2. **JavaScript execution**: `Enabled`
3. **Local storage**: `Enabled`
4. **Session storage**: `Enabled`
5. **IndexedDB**: `Enabled`

#### Загальні налаштування:
```
✅ ОБОВ'ЯЗКОВО УВІМКНУТИ:
- JavaScript execution
- Local Storage access
- Session Storage access
- DOM Storage
- Web SQL Database

❌ НЕ БЛОКУВАТИ:
- Third-party scripts
- Cross-origin requests
- Browser APIs
- Extension APIs
```

**ТЕСТ ФУНКЦІОНАЛЬНОСТІ:**
```javascript
// Вставте в консоль браузера:
try {
  localStorage.setItem('test', 'ok');
  console.log('✅ localStorage працює:', localStorage.getItem('test'));
  localStorage.removeItem('test');
} catch(e) {
  console.log('❌ localStorage заблокований:', e.message);
}
```

### 🔧 УНІВЕРСАЛЬНІ НАЛАШТУВАННЯ ДЛЯ ВСІХ БРАУЗЕРІВ

#### Мінімальні вимоги:
- **JavaScript**: Увімкнений
- **Cookies**: Дозволені для ads.tiktok.com
- **Local Storage**: Доступний
- **Extensions**: Дозволені
- **Developer Tools**: Доступні (F12)

#### Рекомендовані налаштування proxy:
- **HTTP/HTTPS**: Підтримка обох протоколів
- **WebRTC**: Не блокувати повністю
- **DNS**: Використовувати системний або *******
- **Timeout**: Мінімум 30 секунд

## 📋 ПОКРОКОВА ІНСТРУКЦІЯ ДЛЯ ПОЧАТКІВЦІВ

### Крок 1: Підготовка браузера (5 хвилин)

#### Для Browser.Vision:
1. **Відкрийте Browser.Vision**
2. **Створіть новий профіль** або відкрийте існуючий
3. **Перейдіть в Settings профілю**
4. **Знайдіть розділ "Browser Settings"**
5. **Переконайтеся що увімкнено:**
   - JavaScript: ✅ Enabled
   - Local Storage: ✅ Enabled
   - Cookies: ✅ Enabled
6. **Збережіть налаштування**
7. **Запустіть профіль**

#### Для Octo Browser:
1. **Запустіть Octo Browser**
2. **Виберіть профіль** (або створіть новий)
3. **Клікніть "Edit Profile"**
4. **Вкладка "Browser":**
   - JavaScript execution: ✅ Enabled
   - Local storage: ✅ Enabled
   - Session storage: ✅ Enabled
5. **Вкладка "Privacy":**
   - Block third-party cookies: ❌ Disabled
   - Block scripts: ❌ Disabled
6. **Збережіть та запустіть профіль**

### Крок 2: Встановлення розширення (3 хвилини)

1. **В браузері введіть:** `chrome://extensions/`
2. **Увімкніть "Developer mode"** (toggle справа вгорі)
3. **Натисніть "Load unpacked"** (Завантажити розпаковане)
4. **Виберіть папку TT-ext** з файлами розширення
5. **Перевірте що розширення з'явилось** в списку
6. **Переконайтеся що toggle "Enabled"** увімкнений

### Крок 3: Налаштування Airtable (10 хвилин)

#### Створення таблиці:
1. **Перейдіть на** [airtable.com](https://airtable.com)
2. **Увійдіть в акаунт** або зареєструйтеся
3. **Натисніть "Create a base"** → "Start from scratch"
4. **Назвіть базу:** "TikTok Ads Data"
5. **Створіть мінімальні поля** (ТОЧНО ЯК НАПИСАНО):

```
Поле 1: ID (Single line text) - Primary field
Поле 2: Type (Single select) - опції: Campaign, AdGroup, Ad
Поле 3: Name (Single line text)
Поле 4: Status (Single select) - опції: Active, Paused, Ended
Поле 5: Spend (Currency) - USD, precision 2
```

#### Отримання API даних:
1. **Натисніть "Help"** (правий верхній кут в Airtable)
2. **Виберіть "API documentation"**
3. **Скопіюйте Base ID** з URL (appXXXXXXXXXXXXXX)
4. **Перейдіть на** [airtable.com/create/tokens](https://airtable.com/create/tokens)
5. **Створіть новий токен** з правами на вашу базу
6. **Скопіюйте API Key**

### Крок 4: Підключення розширення (1 хвилина!)

1. **Перейдіть на** [ads.tiktok.com](https://ads.tiktok.com)
2. **Увійдіть в акаунт** TikTok Ads
3. **Натисніть на іконку розширення** (в панелі браузера)
4. **Клікніть "Показати налаштування"**
5. **ПРОСТИЙ СПОСІБ - вставте URL таблиці:**
   - Скопіюйте URL з Airtable (повний адрес таблиці)
   - Вставте в поле "Airtable URL"
   - Base ID і Table ID заповняться автоматично! 🎉
6. **Або заповніть вручну:**
   - API Key: (вставте токен з Airtable)
   - Base ID: (appXXXXXXXXXXXXXX)
   - Table ID: (tblXXXXXXXXXXXXXX)
7. **Натисніть "Тест підключення"**
8. **Якщо тест успішний** - натисніть "Зберегти"

### Крок 5: Перший тест (1 хвилина)

1. **Перейдіть на сторінку з кампаніями** в TikTok Ads
2. **Зачекайте повного завантаження** (5-10 секунд)
3. **Натисніть на іконку розширення**
4. **Клікніть "Витягти дані"**
5. **Перевірте в Airtable** - дані повинні з'явитись

### 🔧 Налаштування для стабільної роботи

#### Правила використання:
- ✅ **Максимум 25 записів за раз** (для початківців)
- ✅ **Пауза 5 секунд між витягуваннями**
- ✅ **Не більше 100 записів за годину**
- ✅ **Тестуйте на одному профілі спочатку**
- ❌ **НЕ натискайте кнопки швидко підряд**

### 🚨 Критичні помилки

#### Розширення не завантажується:
```
Manifest version not supported
```
**Вирішення:** Використовуйте Chrome 88+ або Chromium-based браузер

#### Доступ заборонено:
```
Access denied to TikTok Ads
```
**Вирішення:** Увійдіть в TikTok Ads Manager перед використанням

## 📞 Підтримка

Для звітів про помилки або пропозицій:
1. Створіть issue в репозиторії
2. Опишіть проблему детально
3. Додайте скріншоти якщо потрібно
4. Вкажіть версію Chrome і розширення
5. **Для антидетект браузерів**: вкажіть назву та версію

**Корисні посилання:**
- [Airtable API Documentation](https://airtable.com/developers/web/api/introduction)
- [Chrome Extensions Developer Guide](https://developer.chrome.com/docs/extensions/)
- [Browser.Vision Support](https://browser.vision/support)
- [Octo Browser Help](https://octobrowser.net/help)

## 📄 Ліцензія

MIT License - використовуйте вільно для особистих і комерційних проектів.

---

**🎉 Готово! Ваше Chrome розширення для TikTok Ads готове до використання в будь-якому Chromium браузері!**

**🕵️ Протестовано з:**
- ✅ Google Chrome
- ✅ Browser.Vision
- ✅ Octo Browser
- ✅ Інші Chromium-based браузери