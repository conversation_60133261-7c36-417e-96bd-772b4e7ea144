// TikTok Ads Extractor v3.0.3 - Anti-cache implementation
console.log('🚀 TikTok Ads Extractor v3.0.3 loaded successfully');
console.log('🔄 Cache-busting enabled for maximum compatibility');

class TikTokAdsExtractor {
  constructor() {
    console.log('🔧 Constructor initialized - v3.0.3');
    this.extractedData = null;
    this.preventTabOpening();
    this.init();
  }

  preventTabOpening() {
    // Prevent popup from opening as a separate tab
    if (window.location.protocol === 'chrome-extension:' &&
        window.location.pathname.includes('popup.html') &&
        !window.chrome?.extension?.getViews) {
      // If popup is opened as a tab instead of popup - close it
      console.log('INFO: popup.html opened as tab, closing for security...');
      window.close();
      return;
    }
  }

  async init() {
    console.log('🔧 Init called');
    console.log('🔧 DOM ready state:', document.readyState);
    console.log('🔧 Document body:', document.body);

    // Перевіряємо наявність ключових елементів
    const extractBtn = document.getElementById('extractData');
    const sendBtn = document.getElementById('sendToAirtable');
    const actionsDiv = document.querySelector('.actions');

    console.log('🔧 Extract button found:', !!extractBtn);
    console.log('🔧 Send button found:', !!sendBtn);
    console.log('🔧 Actions div found:', !!actionsDiv);

    if (!extractBtn || !sendBtn) {
      console.error('❌ Critical UI elements missing!');
      console.log('🔧 Available elements:', document.querySelectorAll('*').length);
    }

    await this.loadSettings();
    await this.loadVersion();
    this.bindEvents();
    this.updateButtonStates();
    this.updateSettingsSummary();
  }

  async loadSettings() {
    const settings = await chrome.storage.sync.get(['apiKey', 'baseId', 'tableId', 'tableUrl']);

    // Load visible settings fields
    if (settings.apiKey) document.getElementById('apiKey').value = settings.apiKey;
    if (settings.tableUrl) document.getElementById('tableUrl').value = settings.tableUrl;

    // Load hidden settings fields
    if (settings.baseId) document.getElementById('baseId').value = settings.baseId;
    if (settings.tableId) document.getElementById('tableId').value = settings.tableId;

    // If URL exists but hidden fields are missing - parse them
    if (settings.tableUrl && (!settings.baseId || !settings.tableId)) {
      this.parseUrlOnPaste();
    }
  }

  async loadVersion() {
    try {
      const manifest = chrome.runtime.getManifest();
      const version = manifest.version;

      // Update version in header badge
      const versionBadge = document.getElementById('versionBadge');
      if (versionBadge) {
        versionBadge.textContent = `v${version}`;
      }

      // Update version in footer
      const footerVersion = document.getElementById('footerVersion');
      if (footerVersion) {
        footerVersion.textContent = version;
      }
    } catch (error) {
      console.warn('Could not load version from manifest:', error);
    }
  }

  bindEvents() {
    // Main action buttons
    document.getElementById('saveSettings').addEventListener('click', () => this.saveSettings());
    document.getElementById('extractData').addEventListener('click', () => this.extractData());
    document.getElementById('sendToAirtable').addEventListener('click', () => this.sendToAirtable());
    
    // Settings panel toggle
    const toggleBtn = document.getElementById('toggleSettings');
    console.log('🔧 Toggle button found:', toggleBtn);
    if (toggleBtn) {
      toggleBtn.addEventListener('click', () => {
        console.log('🔧 Toggle button clicked!');
        this.toggleSettingsPanel();
      });
    } else {
      console.error('🔧 Toggle button not found!');
    }
    document.getElementById('testConnection').addEventListener('click', () => this.testConnection());
    
    // Clear buttons
    document.getElementById('clearApiKey').addEventListener('click', () => this.clearField('apiKey'));
    document.getElementById('clearTableUrl').addEventListener('click', () => this.clearTableUrl());

    // Parse URL only on paste - optimized for better UX
    document.getElementById('tableUrl').addEventListener('paste', () => {
      setTimeout(() => this.parseUrlOnPaste(), 100); // Delay to allow paste to complete
    });

    // Toggle API key visibility
    document.getElementById('toggleApiKey').addEventListener('click', () => this.toggleApiKeyVisibility());

    // Update summary when API key changes
    document.getElementById('apiKey').addEventListener('input', () => this.updateSettingsSummary());
  }

  async saveSettings() {
    const apiKey = document.getElementById('apiKey').value.trim();
    const tableUrl = document.getElementById('tableUrl').value.trim();

    if (!apiKey) {
      this.showStatus('❌ Please enter API Key!', 'error');
      return;
    }

    if (!tableUrl) {
      this.showStatus('❌ Please paste Airtable table URL!', 'error');
      return;
    }

    // Automatically parse URL
    const parsedData = this.extractAirtableData(tableUrl);

    if (!parsedData.baseId || !parsedData.tableId) {
      this.showStatus('❌ Invalid Airtable URL! Please check the link.', 'error');
      return;
    }

    // Save all settings
    await chrome.storage.sync.set({
      apiKey,
      tableUrl,
      baseId: parsedData.baseId,
      tableId: parsedData.tableId
    });

    // Update hidden fields
    document.getElementById('baseId').value = parsedData.baseId;
    document.getElementById('tableId').value = parsedData.tableId;

    this.showStatus('✅ Settings saved! Ready to work!', 'success');
    this.updateButtonStates();
    this.updateSettingsSummary();
  }

  async extractData() {
    this.showStatus('🔄 Extracting data...', 'loading');
    
    try {
      const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
      
      if (!tab.url.includes('ads.tiktok.com')) {
        throw new Error('Please open TikTok Ads Manager page');
      }

      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        function: this.extractTikTokData
      });

      this.extractedData = results[0].result;
      
      if (this.extractedData && this.extractedData.length > 0) {
        this.displayExtractedData();
        this.showStatus(`✅ Extracted ${this.extractedData.length} records`, 'success');
        this.updateButtonStates();
      } else {
        throw new Error('No data found on current page');
      }
    } catch (error) {
      this.showStatus(`❌ Error: ${error.message}`, 'error');
    }
  }

  extractTikTokData() {
    const data = [];
    
    // Selectors for different TikTok Ads data types
    const campaignRows = document.querySelectorAll('[data-testid="campaign-row"], .campaign-row, [class*="campaign"]');
    const adGroupRows = document.querySelectorAll('[data-testid="adgroup-row"], .adgroup-row, [class*="adgroup"]');
    const adRows = document.querySelectorAll('[data-testid="ad-row"], .ad-row, [class*="ad-item"]');
    
    // Function to extract text from element
    const getText = (element) => element ? element.textContent.trim() : '';
    
    // Function to find numeric values
    const getNumber = (text) => {
      const match = text.match(/[\d,]+\.?\d*/);
      return match ? parseFloat(match[0].replace(/,/g, '')) : null;
    };

    // Extract campaign data
    campaignRows.forEach((row, index) => {
      try {
        const cells = row.querySelectorAll('td, div[class*="cell"], [class*="column"]');
        
        const campaignData = {
          type: 'campaign',
          id: `campaign_${index}`,
          name: getText(row.querySelector('[class*="name"], [data-testid*="name"]')) || `Campaign ${index + 1}`,
          status: getText(row.querySelector('[class*="status"], [data-testid*="status"]')),
          budget: getText(row.querySelector('[class*="budget"], [data-testid*="budget"]')),
          spend: getText(row.querySelector('[class*="spend"], [class*="cost"]')),
          impressions: getText(row.querySelector('[class*="impression"], [data-testid*="impression"]')),
          clicks: getText(row.querySelector('[class*="click"], [data-testid*="click"]')),
          ctr: getText(row.querySelector('[class*="ctr"], [data-testid*="ctr"]')),
          cpc: getText(row.querySelector('[class*="cpc"], [data-testid*="cpc"]')),
          cpm: getText(row.querySelector('[class*="cpm"], [data-testid*="cpm"]')),
          conversions: getText(row.querySelector('[class*="conversion"], [data-testid*="conversion"]')),
          extractedAt: new Date().toISOString(),
          url: window.location.href
        };
        
        data.push(campaignData);
      } catch (error) {
        console.warn('Error extracting campaign data:', error);
      }
    });

    // Extract ad group data
    adGroupRows.forEach((row, index) => {
      try {
        const adGroupData = {
          type: 'adgroup',
          id: `adgroup_${index}`,
          name: getText(row.querySelector('[class*="name"], [data-testid*="name"]')) || `AdGroup ${index + 1}`,
          status: getText(row.querySelector('[class*="status"], [data-testid*="status"]')),
          budget: getText(row.querySelector('[class*="budget"], [data-testid*="budget"]')),
          spend: getText(row.querySelector('[class*="spend"], [class*="cost"]')),
          impressions: getText(row.querySelector('[class*="impression"], [data-testid*="impression"]')),
          clicks: getText(row.querySelector('[class*="click"], [data-testid*="click"]')),
          extractedAt: new Date().toISOString(),
          url: window.location.href
        };
        
        data.push(adGroupData);
      } catch (error) {
        console.warn('Error extracting adgroup data:', error);
      }
    });

    // Extract ad data
    adRows.forEach((row, index) => {
      try {
        const adData = {
          type: 'ad',
          id: `ad_${index}`,
          name: getText(row.querySelector('[class*="name"], [data-testid*="name"]')) || `Ad ${index + 1}`,
          status: getText(row.querySelector('[class*="status"], [data-testid*="status"]')),
          spend: getText(row.querySelector('[class*="spend"], [class*="cost"]')),
          impressions: getText(row.querySelector('[class*="impression"], [data-testid*="impression"]')),
          clicks: getText(row.querySelector('[class*="click"], [data-testid*="click"]')),
          extractedAt: new Date().toISOString(),
          url: window.location.href
        };
        
        data.push(adData);
      } catch (error) {
        console.warn('Error extracting ad data:', error);
      }
    });

    // If standard selectors failed, try generic approach
    if (data.length === 0) {
      const allRows = document.querySelectorAll('tr, [role="row"], .table-row, [class*="row"]');
      
      allRows.forEach((row, index) => {
        const cells = row.querySelectorAll('td, th, div, span');
        if (cells.length >= 3) {
          const rowData = {
            type: 'generic',
            id: `row_${index}`,
            data: Array.from(cells).map(cell => getText(cell)).filter(text => text.length > 0),
            extractedAt: new Date().toISOString(),
            url: window.location.href
          };
          
          if (rowData.data.length > 0) {
            data.push(rowData);
          }
        }
      });
    }

    return data;
  }

  displayExtractedData() {
    const container = document.getElementById('dataContainer');
    
    if (!this.extractedData || this.extractedData.length === 0) {
      container.innerHTML = '<p class="no-data">No data found</p>';
      return;
    }

    const dataHtml = this.extractedData.map(item => `
      <div class="data-item">
        <div class="data-header">
          <span class="data-type">${item.type.toUpperCase()}</span>
          <span class="data-name">${item.name || item.id}</span>
        </div>
        <div class="data-details">
          ${Object.entries(item)
            .filter(([key, value]) => key !== 'type' && key !== 'id' && value)
            .map(([key, value]) => `<span class="data-field"><strong>${key}:</strong> ${value}</span>`)
            .join('')}
        </div>
      </div>
    `).join('');

    container.innerHTML = dataHtml;
  }

  async sendToAirtable() {
    if (!this.extractedData || this.extractedData.length === 0) {
      this.showStatus('❌ Please extract data first', 'error');
      return;
    }

    const settings = await chrome.storage.sync.get(['apiKey', 'baseId', 'tableId']);
    
    if (!settings.apiKey || !settings.baseId || !settings.tableId) {
      this.showStatus('❌ Please configure Airtable connection', 'error');
      return;
    }

    this.showStatus('📤 Sending to Airtable...', 'loading');

    try {
      const records = this.extractedData.map(item => ({
        fields: {
          'Type': item.type,
          'Name': item.name || item.id,
          'Status': item.status || '',
          'Data': JSON.stringify(item),
          'Extracted At': item.extractedAt,
          'URL': item.url
        }
      }));

      const response = await fetch(`https://api.airtable.com/v0/${settings.baseId}/${settings.tableId}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${settings.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ records })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error?.message || 'Airtable API error');
      }

      const result = await response.json();
      this.showStatus(`✅ Successfully sent ${result.records.length} records`, 'success');
      
    } catch (error) {
      this.showStatus(`❌ Send error: ${error.message}`, 'error');
    }
  }

  updateButtonStates() {
    const extractBtn = document.getElementById('extractData');
    const sendBtn = document.getElementById('sendToAirtable');
    
    sendBtn.disabled = !this.extractedData || this.extractedData.length === 0;
  }

  showStatus(message, type) {
    const statusDiv = document.getElementById('status');
    statusDiv.textContent = message;
    statusDiv.className = `status ${type}`;
    statusDiv.classList.remove('hidden');

    if (type === 'success' || type === 'error') {
      setTimeout(() => {
        statusDiv.classList.add('hidden');
      }, 5000);
    }
  }

  showConnectionStatus(message, type) {
    const statusDiv = document.getElementById('connectionStatus');
    statusDiv.textContent = message;
    statusDiv.className = `connection-status ${type}`;
    statusDiv.classList.remove('hidden');

    if (type === 'success' || type === 'error') {
      setTimeout(() => {
        statusDiv.classList.add('hidden');
      }, 3000);
    }
  }

  toggleSettingsPanel() {
    console.log('🔧🔧🔧 toggleSettingsPanel called! 🔧🔧🔧');

    const panel = document.getElementById('settingsPanel');
    const toggleText = document.getElementById('toggleText');
    const toggleIcon = document.getElementById('toggleIcon');

    console.log('🔧 Elements found:', { panel, toggleText, toggleIcon });
    console.log('🔧 Panel exists:', !!panel);
    console.log('🔧 Panel classList:', panel?.classList);
    console.log('🔧 Panel has hidden class:', panel?.classList.contains('hidden'));
    console.log('🔧 Panel style display:', panel?.style.display);
    console.log('🔧 Panel computed style:', window.getComputedStyle(panel)?.display);

    if (panel && panel.classList.contains('hidden')) {
      console.log('🔧 REMOVING HIDDEN CLASS');
      panel.classList.remove('hidden');
      if (toggleText) toggleText.textContent = 'Hide settings';
      if (toggleIcon) toggleIcon.textContent = '🙈';
      console.log('🔧 Panel shown - new classList:', panel.classList);
    } else if (panel) {
      console.log('🔧 ADDING HIDDEN CLASS');
      panel.classList.add('hidden');
      if (toggleText) toggleText.textContent = 'Show settings';
      if (toggleIcon) toggleIcon.textContent = '👁️';
      console.log('🔧 Panel hidden - new classList:', panel.classList);
    } else {
      console.error('🔧 Panel not found!');
    }
  }

  async testConnection() {
    const apiKey = document.getElementById('apiKey').value;
    const baseId = document.getElementById('baseId').value;
    const tableId = document.getElementById('tableId').value;

    if (!apiKey || !baseId || !tableId) {
      this.showConnectionStatus('❌ Fill all fields', 'error');
      return;
    }

    this.showConnectionStatus('🔄 Testing...', 'loading');

    try {
      const response = await chrome.runtime.sendMessage({
        action: 'validateAirtableConnection',
        settings: { apiKey, baseId, tableId }
      });

      if (response.success) {
        this.showConnectionStatus('✅ Connection successful!', 'success');
      } else {
        this.showConnectionStatus(`❌ ${response.error}`, 'error');
      }
    } catch (error) {
      this.showConnectionStatus(`❌ Error: ${error.message}`, 'error');
    }
  }

  clearField(fieldId) {
    document.getElementById(fieldId).value = '';
    this.updateSettingsSummary();
  }

  clearTableUrl() {
    document.getElementById('tableUrl').value = '';
    document.getElementById('baseId').value = '';
    document.getElementById('tableId').value = '';
    this.updateSettingsSummary();
    this.showStatus('🗑️ URL cleared', 'success');
  }

  parseTableUrl() {
    const tableUrl = document.getElementById('tableUrl').value.trim();

    if (!tableUrl) {
      this.showStatus('❌ Enter Airtable table URL', 'error');
      return;
    }

    try {
      const parsedData = this.extractAirtableData(tableUrl);

      if (parsedData.baseId) {
        document.getElementById('baseId').value = parsedData.baseId;
      }

      if (parsedData.tableId) {
        document.getElementById('tableId').value = parsedData.tableId;
      }

      if (parsedData.baseId || parsedData.tableId) {
        this.showStatus('✅ Data extracted from URL!', 'success');
        this.updateSettingsSummary();
      } else {
        this.showStatus('❌ Could not extract data from URL', 'error');
      }
    } catch (error) {
      this.showStatus(`❌ URL parsing error: ${error.message}`, 'error');
    }
  }

  parseUrlOnPaste() {
    const tableUrl = document.getElementById('tableUrl').value.trim();

    if (!tableUrl) return;

    // Parse only if this is an Airtable URL
    if (tableUrl.includes('airtable.com')) {
      try {
        const parsedData = this.extractAirtableData(tableUrl);

        if (parsedData.baseId && parsedData.tableId) {
          // Found everything - fill fields and celebrate
          document.getElementById('baseId').value = parsedData.baseId;
          document.getElementById('tableId').value = parsedData.tableId;
          this.updateSettingsSummary();
          this.showStatus('✅ URL recognized! Ready to save!', 'success');
        }
      } catch (error) {
        // Silently ignore paste errors
      }
    }
  }

  extractAirtableData(url) {
    const result = { baseId: null, tableId: null };

    console.log('Parsing URL:', url);

    // Extract Base ID (app + 14 characters)
    const baseIdMatch = url.match(/app[a-zA-Z0-9]{14}/);
    if (baseIdMatch) {
      result.baseId = baseIdMatch[0];
      console.log('Found Base ID:', result.baseId);
    }

    // Extract Table ID (tbl + 14 characters)
    const tableIdMatch = url.match(/tbl[a-zA-Z0-9]{14}/);
    if (tableIdMatch) {
      result.tableId = tableIdMatch[0];
      console.log('Found Table ID:', result.tableId);
    }

    // Alternative Base ID search without "app" prefix
    if (!result.baseId) {
      const altBaseMatch = url.match(/airtable\.com\/([a-zA-Z0-9]{17})/);
      if (altBaseMatch) {
        result.baseId = altBaseMatch[1];
        console.log('Found alternative Base ID:', result.baseId);
      }
    }

    console.log('Extraction result:', result);
    return result;
  }

  toggleApiKeyVisibility() {
    const apiKeyInput = document.getElementById('apiKey');
    const toggleBtn = document.getElementById('toggleApiKey');
    
    if (apiKeyInput.type === 'password') {
      apiKeyInput.type = 'text';
      toggleBtn.textContent = '🙈';
      toggleBtn.title = 'Hide';
    } else {
      apiKeyInput.type = 'password';
      toggleBtn.textContent = '👁️';
      toggleBtn.title = 'Show';
    }
  }

  updateSettingsSummary() {
    const apiKey = document.getElementById('apiKey').value;
    const baseId = document.getElementById('baseId').value;
    const tableId = document.getElementById('tableId').value;

    const statusEl = document.getElementById('summaryStatus');
    const baseEl = document.getElementById('summaryBase');
    const tableEl = document.getElementById('summaryTable');

    // Status
    if (apiKey && baseId && tableId) {
      statusEl.textContent = 'Configured ✅';
      statusEl.className = 'summary-value configured';
    } else {
      statusEl.textContent = 'Not configured ❌';
      statusEl.className = 'summary-value not-configured';
    }

    // Base
    if (baseId) {
      baseEl.textContent = baseId.length > 12 ? baseId.substring(0, 12) + '...' : baseId;
      baseEl.className = 'summary-value';
    } else {
      baseEl.textContent = '-';
      baseEl.className = 'summary-value empty';
    }

    // Table
    if (tableId) {
      tableEl.textContent = tableId.length > 15 ? tableId.substring(0, 15) + '...' : tableId;
      tableEl.className = 'summary-value';
    } else {
      tableEl.textContent = '-';
      tableEl.className = 'summary-value empty';
    }
  }
}

// Initialize after DOM load
document.addEventListener('DOMContentLoaded', () => {
  console.log('🔧 DOM loaded, initializing TikTokAdsExtractor...');
  try {
    new TikTokAdsExtractor();
    console.log('✅ TikTokAdsExtractor initialized successfully');
  } catch (error) {
    console.error('❌ Error initializing TikTokAdsExtractor:', error);
  }
});

// Fallback initialization if DOMContentLoaded already fired
if (document.readyState === 'loading') {
  console.log('🔧 Document still loading, waiting for DOMContentLoaded...');
} else {
  console.log('🔧 Document already loaded, initializing immediately...');
  try {
    new TikTokAdsExtractor();
    console.log('✅ TikTokAdsExtractor initialized successfully (fallback)');
  } catch (error) {
    console.error('❌ Error initializing TikTokAdsExtractor (fallback):', error);
  }
}