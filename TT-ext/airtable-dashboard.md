# 📋 **Покрокова інструкція створення TikTok Ads Dashboard**

## **🎯 Крок 1: Базові налаштування**
1. **Виберіть "Dashboard"** → **"Next"**
2. **Виберіть "Desktop"** → **"Next"**
3. **Назва:** "TikTok Ads Analytics"
4. **Натисніть "Create"**

---

## **📊 Крок 2: Додавання KPI (4 числа в ряд)**

### **2.1 Перший KPI - Total Spend:**
1. **Натисніть "+"** → **"Number"**
2. **Data source:** "TikTok Ads Data"
3. **Aggregation:** "Sum"
4. **Field:** "Spend"
5. **Title:** "Total Spend"
6. **Format:** Currency
7. **Натисніть "Save"**

### **2.2 Другий KPI - Total Impressions:**
1. **Натисніть "+"** → **"Number"**
2. **Aggregation:** "Sum"
3. **Field:** "Impressions"
4. **Title:** "Total Impressions"
5. **Format:** Number
6. **Натисніть "Save"**

### **2.3 Третій KPI - Average CTR:**
1. **Натисніть "+"** → **"Number"**
2. **Aggregation:** "Average"
3. **Field:** "CTR"
4. **Title:** "Average CTR"
5. **Format:** Percentage
6. **Натисніть "Save"**

### **2.4 Четвертий KPI - Total Conversions:**
1. **Натисніть "+"** → **"Number"**
2. **Aggregation:** "Sum"
3. **Field:** "Conversions"
4. **Title:** "Total Conversions"
5. **Format:** Number
6. **Натисніть "Save"**

---

## **📈 Крок 3: Додавання графіків**

### **3.1 Перший графік - Performance Chart:**
1. **Натисніть "+"** → **"Chart"**
2. **Chart type:** "Bar chart"
3. **X-axis:** "Name"
4. **Y-axis:** "Performance Score"
5. **Group by:** "Type"
6. **Title:** "Performance by Campaign"
7. **Натисніть "Save"**

### **3.2 Другий графік - Spend vs ROAS:**
1. **Натисніть "+"** → **"Chart"**
2. **Chart type:** "Scatter plot"
3. **X-axis:** "Spend"
4. **Y-axis:** "ROAS"
5. **Color by:** "Type"
6. **Title:** "Spend vs ROAS"
7. **Натисніть "Save"**

---

## **🔍 Крок 4: Додавання фільтрів**

### **4.1 Фільтр по типу:**
1. **Натисніть "+"** → **"Filter"**
2. **Field:** "Type"
3. **Style:** "Dropdown"
4. **Title:** "Campaign Type"
5. **Натисніть "Save"**

### **4.2 Фільтр по статусу:**
1. **Натисніть "+"** → **"Filter"**
2. **Field:** "Status"
3. **Style:** "Button group"
4. **Title:** "Status"
5. **Натисніть "Save"**

---

## **📋 Крок 5: Додавання таблиці**

1. **Натисніть "+"** → **"Record list"**
2. **Fields to show:** (виберіть тільки ці поля)
   - Name
   - Type
   - Status
   - Spend
   - Impressions
   - Clicks
   - CTR
   - Conversions
   - Performance Score
3. **Sort by:** "Performance Score" (Descending)
4. **Enable search:** ✅
5. **Title:** "Campaign Data"
6. **Натисніть "Save"**

---

## **📱 Крок 6: Налаштування responsive**

### **Для кожного елемента:**
1. **Клікніть на елемент**
2. **Settings** (⚙️)
3. **Layout:**
   - **Desktop:** 4 колонки (для KPI)
   - **Tablet:** 2 колонки
   - **Mobile:** 1 колонка

---

## **🔧 Додаткові налаштування**

### **Кольори та стилі:**
- **Theme:** Dark або Light (за вибором)
- **Accent color:** Blue або Brand color
- **Performance Score colors:** 
  - Green для "Excellent"
  - Yellow для "Good"
  - Orange для "Average"
  - Red для "Poor"

### **Загальні фільтри сторінки:**
1. **Page settings** → **Filters**
2. **Add filter:** Status = "Active" (показувати тільки активні)
3. **Add filter:** Date range = "Last 30 days"

---

## **✅ Результат**

Ваш дашборд буде мати структуру:

```
┌─────────────────────────────────────────────────────┐
│ [Total Spend] [Impressions] [CTR] [Conversions]     │
├─────────────────────────────────────────────────────┤
│ [Performance Chart]    │    [Spend vs ROAS]        │
├─────────────────────────────────────────────────────┤
│ [Type Filter] [Status Filter]                      │
├─────────────────────────────────────────────────────┤
│ [Compact Data Table with key metrics]              │
└─────────────────────────────────────────────────────┘
```

---

## **📊 Формули для полів (додати після створення дашборду)**

### **Calculated CTR:**
```
IF({Impressions} > 0, {Clicks} / {Impressions}, 0)
```

### **Calculated Conversion Rate:**
```
IF({Clicks} > 0, {Conversions} / {Clicks}, 0)
```

### **Calculated Cost per Conversion:**
```
IF({Conversions} > 0, {Spend} / {Conversions}, 0)
```

### **Performance Score:**
```
IF(
  AND({CTR} >= 0.02, {Conversions} >= 5), 
  "Excellent", 
  IF(
    AND({CTR} >= 0.01, {Conversions} >= 1), 
    "Good", 
    IF(
      {CTR} >= 0.005, 
      "Average", 
      "Poor"
    )
  )
)
```

### **Estimated Profit:**
```
({Conversions} * 50) - {Spend}
```
*Замініть 50 на вашу середню вартість конверсії*

### **Video Engagement Score:**
```
IF(
  {Video Views} > 0,
  ({Video 100% Views} / {Video Views}) * 100,
  0
)
```

### **Social Engagement Rate:**
```
IF(
  {Video Views} > 0,
  (({Likes} + {Shares} + {Comments}) / {Video Views}) * 100,
  0
)
```

### **Quality Score:**
```
IF(
  AND({CTR} > 0, {Conversion Rate} > 0),
  ROUND(
    ({CTR} * 100 * 0.4) + 
    ({Conversion Rate} * 100 * 0.4) + 
    (IF({Video Completion Rate} > 0, {Video Completion Rate} * 100 * 0.2, 0)),
    1
  ),
  0
)
```

---

## **🎯 Підсумок**

- **Час виконання:** 20-25 хвилин
- **Елементів:** 4 KPI + 2 графіки + 2 фільтри + 1 таблиця
- **Responsive:** Адаптується під Desktop/Tablet/Mobile
- **Функціонал:** Повний аналіз TikTok реклами

**Готово!** 🎉

---

*Створено для аналізу TikTok Ads кампаній*  
*Версія: 1.0*  
*Дата: 2025*