# 📊 Airtable Table Setup Guide - TikTok Ads Extractor

Детальна інструкція по створенню таблиці Airtable для роботи з TikTok Ads Extractor v3.0.3

## 🚀 Швидкий старт - Мінімальна конфігурація

### Обов'язкові поля (БЕЗ НИХ РОЗШИРЕННЯ НЕ ПРАЦЮВАТИМЕ)

| Назва поля | Тип поля | Опції | Опис |
|------------|----------|-------|------|
| `ID` | Single line text | **Primary field** | Унікальний ідентифікатор запису |
| `Type` | Single select | Campaign, AdGroup, Ad | Тип об'єкта TikTok Ads |
| `Name` | Single line text | - | Назва кампанії/групи/оголошення |
| `Status` | Single select | Active, Paused, Ended | Статус об'єкта |

### Створення мінімальної таблиці

1. **Створіть нову базу**:
   - Перейдіть на [airtable.com](https://airtable.com)
   - Натисніть "Create a base" → "Start from scratch"
   - Назвіть базу: "TikTok Ads Data"

2. **Налаштуйте 4 обов'язкові поля**:

#### Поле 1: ID (Primary Field)
```
Назва: ID
Тип: Single line text
Primary field: ✅ Так
Опис: Унікальний ідентифікатор для кожного запису
```

#### Поле 2: Type
```
Назва: Type
Тип: Single select
Опції: 
  - Campaign
  - AdGroup
  - Ad
Колір опцій: Campaign (синій), AdGroup (зелений), Ad (помаранчевий)
```

#### Поле 3: Name
```
Назва: Name
Тип: Single line text
Опис: Назва кампанії, групи оголошень або оголошення
```

#### Поле 4: Status
```
Назва: Status
Тип: Single select
Опції:
  - Active (зелений)
  - Paused (жовтий)
  - Ended (червоний)
  - Draft (сірий)
```

**✅ З цими 4 полями розширення вже працюватиме!**

---

## 💰 Рекомендована конфігурація - Основні метрики

Додайте ці поля для збору ключових метрик:

| Назва поля | Тип поля | Налаштування | Опис |
|------------|----------|--------------|------|
| `Spend` | Currency | USD, 2 десяткові | Витрачена сума |
| `Impressions` | Number | Integer | Кількість показів |
| `Clicks` | Number | Integer | Кількість кліків |
| `CTR` | Percent | 2 десяткові | Click-through rate |
| `Conversions` | Number | Integer | Кількість конверсій |

### Детальні налаштування полів:

#### Поле: Spend
```
Назва: Spend
Тип: Currency
Валюта: USD ($)
Десяткові знаки: 2
Формат: $1,234.56
Приклад: $125.50
```

#### Поле: Impressions
```
Назва: Impressions
Тип: Number
Формат: Integer (ціле число)
Роздільник тисяч: ✅ Увімкнено (1,000)
Приклад: 15,432
```

#### Поле: Clicks
```
Назва: Clicks
Тип: Number
Формат: Integer
Роздільник тисяч: ✅ Увімкнено
Приклад: 1,234
```

#### Поле: CTR
```
Назва: CTR
Тип: Percent
Десяткові знаки: 2
Формат: 12.34%
Приклад: 8.75%
```

#### Поле: Conversions
```
Назва: Conversions
Тип: Number
Формат: Integer
Приклад: 45
```

---

## 🎯 Повна конфігурація - Всі можливі метрики

### Основні поля для всіх типів об'єктів

| Назва поля | Тип поля | Налаштування | Застосування |
|------------|----------|--------------|--------------|
| `Objective` | Single line text | - | Тільки для Campaign |
| `Budget` | Currency | USD, 2 десяткові | Campaign, AdGroup |
| `CPC` | Currency | USD, 4 десяткові | Всі типи |
| `CPM` | Currency | USD, 2 десяткові | Всі типи |
| `Conversion Rate` | Percent | 2 десяткові | Всі типи |
| `Cost per Conversion` | Currency | USD, 4 десяткові | Всі типи |
| `ROAS` | Number | 2 десяткові | Всі типи |
| `Reach` | Number | Integer | Всі типи |
| `Frequency` | Number | 2 десяткові | Всі типи |

#### Детальні налаштування:

**Budget**
```
Назва: Budget
Тип: Currency
Валюта: USD
Десяткові знаки: 2
Опис: Денний або загальний бюджет
Приклад: $500.00
```

**CPC (Cost Per Click)**
```
Назва: CPC
Тип: Currency
Валюта: USD
Десяткові знаки: 4
Формат: $0.1234
Приклад: $0.2547
```

**CPM (Cost Per Mille)**
```
Назва: CPM
Тип: Currency
Валюта: USD
Десяткові знаки: 2
Опис: Вартість за 1000 показів
Приклад: $12.45
```

**Conversion Rate**
```
Назва: Conversion Rate
Тип: Percent
Десяткові знаки: 2
Формат: 12.34%
Приклад: 3.45%
```

**Cost per Conversion**
```
Назва: Cost per Conversion
Тип: Currency
Валюта: USD
Десяткові знаки: 4
Приклад: $25.4567
```

**ROAS (Return on Ad Spend)**
```
Назва: ROAS
Тип: Number
Десяткові знаки: 2
Формат: 1.23
Опис: Співвідношення доходу до витрат
Приклад: 3.25 (означає $3.25 доходу на $1 витрат)
```

**Reach**
```
Назва: Reach
Тип: Number
Формат: Integer
Роздільник тисяч: ✅ Увімкнено
Опис: Кількість унікальних користувачів
Приклад: 12,500
```

**Frequency**
```
Назва: Frequency
Тип: Number
Десяткові знаки: 2
Опис: Середня кількість показів на користувача
Приклад: 2.35
```

### Відео метрики

| Назва поля | Тип поля | Налаштування | Опис |
|------------|----------|--------------|------|
| `Video Views` | Number | Integer | Всі перегляди відео |
| `Video 2s Views` | Number | Integer | Перегляди понад 2 секунди |
| `Video 6s Views` | Number | Integer | Перегляди понад 6 секунд |
| `Video 25% Views` | Number | Integer | 25% відео переглянуто |
| `Video 50% Views` | Number | Integer | 50% відео переглянуто |
| `Video 75% Views` | Number | Integer | 75% відео переглянуто |
| `Video 100% Views` | Number | Integer | 100% відео переглянуто |
| `Video Play Actions` | Number | Integer | Натискання на відтворення |
| `Video Watch Time` | Duration | hh:mm:ss | Загальний час перегляду |
| `Avg Video Play Time` | Duration | hh:mm:ss | Середній час перегляду |
| `Video Completion Rate` | Percent | 2 десяткові | Відсоток завершених переглядів |

#### Налаштування відео полів:

**Video Views**
```
Назва: Video Views
Тип: Number
Формат: Integer
Роздільник тисяч: ✅ Увімкнено
Приклад: 45,678
```

**Video Watch Time**
```
Назва: Video Watch Time
Тип: Duration
Формат: h:mm:ss
Приклад: 2:15:30 (2 години 15 хвилин 30 секунд)
```

**Video Completion Rate**
```
Назва: Video Completion Rate
Тип: Percent
Десяткові знаки: 2
Приклад: 68.75%
```

### TikTok-специфічні метрики

| Назва поля | Тип поля | Налаштування | Опис |
|------------|----------|--------------|------|
| `Likes` | Number | Integer | Лайки на відео |
| `Shares` | Number | Integer | Поширення |
| `Comments` | Number | Integer | Коментарі |
| `Follows` | Number | Integer | Нові підписники |
| `Profile Views` | Number | Integer | Перегляди профілю |
| `Engagement Rate` | Percent | 2 десяткові | Залученість аудиторії |
| `Social Engagement` | Number | Integer | Загальна соціальна активність |
| `Unique Users` | Number | Integer | Унікальні користувачі |
| `New Users` | Number | Integer | Нові користувачі |
| `Creative Views` | Number | Integer | Перегляди креативу |

#### Налаштування TikTok полів:

**Engagement Rate**
```
Назва: Engagement Rate
Тип: Percent
Десяткові знаки: 2
Формула: (Likes + Shares + Comments) / Impressions * 100
Приклад: 4.25%
```

### Додаткові поля для AdGroups

| Назва поля | Тип поля | Опис |
|------------|----------|------|
| `Placement` | Single line text | Розміщення оголошень |
| `Audience` | Single line text | Цільова аудиторія |
| `Bidding` | Single line text | Стратегія ставок |

**Placement**
```
Назва: Placement
Тип: Single line text
Приклади: "For You Feed", "In-Feed Video", "Top View"
```

### Додаткові поля для Ads

| Назва поля | Тип поля | Опис |
|------------|----------|------|
| `Format` | Single line text | Формат оголошення |
| `Ad Preview` | Attachment | Прев'ю креативу |

**Ad Preview**
```
Назва: Ad Preview
Тип: Attachment
Формати: Зображення, відео
Максимум файлів: 1
```

### Службові поля

| Назва поля | Тип поля | Налаштування | Опис |
|------------|----------|--------------|------|
| `Raw Data` | Long text | - | JSON дані від розширення |
| `URL` | URL | - | Посилання на сторінку TikTok |
| `Extracted At` | Date | Include time | Час витягування даних |
| `Source` | Single select | extension, manual, api | Джерело даних |

**Extracted At**
```
Назва: Extracted At
Тип: Date
Формат: Include time (MM/DD/YYYY HH:MM AM/PM)
GMT offset: Local timezone
Приклад: 12/25/2024 3:45 PM
```

**Source**
```
Назва: Source
Тип: Single select
Опції:
  - extension (синій)
  - manual (зелений)
  - api (помаранчевий)
```

---

## 🧮 Формули для автоматичних розрахунків

### Базові формули

#### Cost per Conversion (якщо не заповнено автоматично)
```
Назва поля: Calculated Cost per Conversion
Тип: Formula
Формула: IF({Conversions} > 0, {Spend} / {Conversions}, 0)
Формат результату: Currency (USD, 4 десяткові)
```

#### CTR (якщо не заповнено автоматично)
```
Назва поля: Calculated CTR
Тип: Formula
Формула: IF({Impressions} > 0, {Clicks} / {Impressions}, 0)
Формат результату: Percent (2 десяткові)
```

#### Conversion Rate (якщо не заповнено автоматично)
```
Назва поля: Calculated Conversion Rate
Тип: Formula
Формула: IF({Clicks} > 0, {Conversions} / {Clicks}, 0)
Формат результату: Percent (2 десяткові)
```

### Аналітичні формули

#### Performance Score
```
Назва поля: Performance Score
Тип: Formula
Формула: 
IF(
  AND({CTR} >= 0.02, {Conversions} >= 5), 
  "Excellent", 
  IF(
    AND({CTR} >= 0.01, {Conversions} >= 1), 
    "Good", 
    IF(
      {CTR} >= 0.005, 
      "Average", 
      "Poor"
    )
  )
)
Формат результату: Single line text
```

#### Profit Estimation (потрібно налаштувати conversion value)
```
Назва поля: Estimated Profit
Тип: Formula
Формула: ({Conversions} * 50) - {Spend}
Примітка: Замініть 50 на вашу середню вартість конверсії
Формат результату: Currency (USD, 2 десяткові)
```

#### Video Engagement Score
```
Назва поля: Video Engagement Score
Тип: Formula
Формула: 
IF(
  {Video Views} > 0,
  ({Video 100% Views} / {Video Views}) * 100,
  0
)
Формат результату: Percent (2 десяткові)
```

#### Social Engagement Rate
```
Назва поля: Social Engagement Rate
Тип: Formula
Формула: 
IF(
  {Video Views} > 0,
  (({Likes} + {Shares} + {Comments}) / {Video Views}) * 100,
  0
)
Формат результату: Percent (2 десяткові)
```

#### Quality Score
```
Назва поля: Quality Score
Тип: Formula
Формула:
IF(
  AND({CTR} > 0, {Conversion Rate} > 0),
  ROUND(
    ({CTR} * 100 * 0.4) + 
    ({Conversion Rate} * 100 * 0.4) + 
    (IF({Video Completion Rate} > 0, {Video Completion Rate} * 100 * 0.2, 0)),
    1
  ),
  0
)
Опис: Зважена оцінка якості (CTR 40% + Conversion Rate 40% + Video Completion 20%)
Формат результату: Number (1 десятковий)
```

---

## 📋 Views (Представлення) для ефективної роботи

### 1. All Data (За замовчуванням)
```
Назва: All Data
Фільтри: Немає
Сортування: Extracted At (Newest first)
Групування: Немає
```

### 2. Active Campaigns Only
```
Назва: Active Campaigns
Фільтри: 
  - Type = "Campaign"
  - Status = "Active"
Сортування: Spend (Largest first)
```

### 3. Top Performers
```
Назва: Top Performers
Фільтри: 
  - CTR > 1%
  - Conversions > 0
Сортування: ROAS (Largest first)
```

### 4. Low Performers
```
Назва: Low Performers
Фільтри:
  - CTR < 0.5%
  OR
  - Conversion Rate < 1%
Сортування: CTR (Smallest first)
```

### 5. Recent Data
```
Назва: Recent Data
Фільтри: 
  - Extracted At (within last 7 days)
Сортування: Extracted At (Newest first)
Групування: Type
```

### 6. Budget Analysis
```
Назва: Budget Analysis
Фільтри: 
  - Type = "Campaign"
  - Budget > 0
Поля: Name, Budget, Spend, (Budget - Spend), Status
Сортування: Budget (Largest first)
```

---

## 🔗 Автоматизація та інтеграції

### Automation правила

#### 1. Позначення низькоефективних кампаній
```
Тригер: Коли запис створюється або змінюється
Умова: CTR < 0.5% AND Spend > $100
Дія: Змінити поле "Status" на "Needs Review"
```

#### 2. Сповіщення про високі витрати
```
Тригер: Коли поле "Spend" змінюється
Умова: Spend > $1000
Дія: Надіслати email-сповіщення
```

### Sync з Google Sheets
Для додаткової аналітики можна налаштувати синхронізацію:

1. Встановіть Airtable Sync for Google Sheets
2. Виберіть поля для синхронізації
3. Налаштуйте автоматичне оновлення

---

## ⚠️ Важливі примітки

### Обмеження Airtable
- Максимум 50,000 записів у базі (Free plan)
- Максимум 1,200 записів за API запит
- Ліміт 5 запитів на секунду

### Поради для ефективності
1. **Використовуйте індекси**: Поля для сортування та фільтрації
2. **Обмежте кількість полів**: Тільки необхідні для аналізу
3. **Регулярно очищайте**: Видаляйте старі тестові дані
4. **Backup дані**: Експортуйте важливі дані в CSV

### Налаштування прав доступу
```
Collaborator permissions:
- Owner: Повний доступ
- Creator: Створення записів, редагування власних
- Editor: Редагування всіх записів
- Commenter: Тільки коментарі
- Read only: Тільки перегляд
```

---

## 🚀 Швидке створення таблиці

### Копіювання полів (для швидкого старту)

1. **Створіть базу Airtable**
2. **Скопіюйте та вставте у поля**:

```csv
Field Name,Field Type,Options
ID,Single line text,Primary field
Type,"Single select","Campaign,AdGroup,Ad"
Name,Single line text,
Status,"Single select","Active,Paused,Ended,Draft"
Spend,Currency,"USD, 2 decimals"
Impressions,Number,Integer
Clicks,Number,Integer
CTR,Percent,2 decimals
Conversions,Number,Integer
```

3. **Імпортуйте як CSV** або створюйте поля вручну
4. **Налаштуйте права доступу**
5. **Отримайте API ключ** на [airtable.com/create/tokens](https://airtable.com/create/tokens)

**✅ Готово! Ваша Airtable таблиця налаштована для роботи з TikTok Ads Extractor!**

## 🎯 Повна таблиця з формулами (Copy-Paste Ready)

### Всі поля включно з формулами для копіювання

```csv
Field Name,Field Type,Field Options,Formula/Description
ID,Single line text,Primary field,Унікальний ідентифікатор
Type,Single select,"Campaign,AdGroup,Ad",Тип об'єкта
Name,Single line text,,Назва кампанії/групи/оголошення
Status,Single select,"Active,Paused,Ended,Draft",Статус об'єкта
Objective,Single line text,,Ціль кампанії (тільки для Campaign)
Budget,Currency,"USD, 2 decimals",Бюджет кампанії/групи
Spend,Currency,"USD, 2 decimals",Витрачена сума
Impressions,Number,Integer,Кількість показів
Clicks,Number,Integer,Кількість кліків
CTR,Percent,2 decimals,Click-through rate
CPC,Currency,"USD, 4 decimals",Вартість за клік
CPM,Currency,"USD, 2 decimals",Вартість за 1000 показів
Conversions,Number,Integer,Кількість конверсій
Conversion Rate,Percent,2 decimals,Відсоток конверсій
Cost per Conversion,Currency,"USD, 4 decimals",Вартість за конверсію
ROAS,Number,2 decimals,Return on Ad Spend
Reach,Number,Integer,Унікальні користувачі
Frequency,Number,2 decimals,Середня частота показів
Video Views,Number,Integer,Перегляди відео
Video 2s Views,Number,Integer,Перегляди понад 2 секунди
Video 6s Views,Number,Integer,Перегляди понад 6 секунд
Video 25% Views,Number,Integer,25% відео переглянуто
Video 50% Views,Number,Integer,50% відео переглянуто
Video 75% Views,Number,Integer,75% відео переглянуто
Video 100% Views,Number,Integer,100% відео переглянуто
Video Play Actions,Number,Integer,Натискання відтворення
Video Watch Time,Duration,h:mm:ss,Загальний час перегляду
Avg Video Play Time,Duration,h:mm:ss,Середній час перегляду
Video Completion Rate,Percent,2 decimals,Відсоток завершених переглядів
Likes,Number,Integer,Лайки на відео
Shares,Number,Integer,Поширення
Comments,Number,Integer,Коментарі
Follows,Number,Integer,Нові підписники
Profile Views,Number,Integer,Перегляди профілю
Engagement Rate,Percent,2 decimals,Рівень залученості
Social Engagement,Number,Integer,Загальна соціальна активність
Unique Users,Number,Integer,Унікальні користувачі
New Users,Number,Integer,Нові користувачі
Creative Views,Number,Integer,Перегляди креативу
Placement,Single line text,,Розміщення (для AdGroup)
Audience,Single line text,,Цільова аудиторія (для AdGroup)
Bidding,Single line text,,Стратегія ставок (для AdGroup)
Format,Single line text,,Формат оголошення (для Ad)
Ad Preview,Attachment,,Прев'ю креативу (для Ad)
Raw Data,Long text,,JSON дані від розширення
URL,URL,,Посилання на TikTok Ads
Extracted At,Date,Include time,Час витягування даних
Source,Single select,"extension,manual,api",Джерело даних
Calculated CTR,Number,4 decimals,ФОРМУЛА: Розраховується автоматично
Calculated Conversion Rate,Number,4 decimals,ФОРМУЛА: Розраховується автоматично
Calculated Cost per Conversion,Currency,"USD, 4 decimals",ФОРМУЛА: Розраховується автоматично
Performance Score,Single line text,,ФОРМУЛА: Оцінка ефективності
Estimated Profit,Currency,"USD, 2 decimals",ФОРМУЛА: Прибуток (потрібно налаштувати)
Video Engagement Score,Percent,2 decimals,ФОРМУЛА: Залученість до відео
Social Engagement Rate,Percent,2 decimals,ФОРМУЛА: Соціальна залученість
Quality Score,Number,1 decimal,ФОРМУЛА: Загальна оцінка якості
```

### Формули для копіювання в Airtable

Після створення всіх полів, замініть поля з "ФОРМУЛА" на Formula type та вставте ці формули:

#### Calculated CTR
```
IF({Impressions} > 0, {Clicks} / {Impressions}, 0)
```

#### Calculated Conversion Rate
```
IF({Clicks} > 0, {Conversions} / {Clicks}, 0)
```

#### Calculated Cost per Conversion
```
IF({Conversions} > 0, {Spend} / {Conversions}, 0)
```

#### Performance Score
```
IF(
  AND({CTR} >= 0.02, {Conversions} >= 5), 
  "Excellent", 
  IF(
    AND({CTR} >= 0.01, {Conversions} >= 1), 
    "Good", 
    IF(
      {CTR} >= 0.005, 
      "Average", 
      "Poor"
    )
  )
)
```

#### Estimated Profit (замініть 50 на вашу вартість конверсії)
```
({Conversions} * 50) - {Spend}
```

#### Video Engagement Score
```
IF(
  {Video Views} > 0,
  ({Video 100% Views} / {Video Views}) * 100,
  0
)
```

#### Social Engagement Rate
```
IF(
  {Video Views} > 0,
  (({Likes} + {Shares} + {Comments}) / {Video Views}) * 100,
  0
)
```

#### Quality Score
```
IF(
  AND({CTR} > 0, {Conversion Rate} > 0),
  ROUND(
    ({CTR} * 100 * 0.4) + 
    ({Conversion Rate} * 100 * 0.4) + 
    (IF({Video Completion Rate} > 0, {Video Completion Rate} * 100 * 0.2, 0)),
    1
  ),
  0
)
```

### Пошаговий процес створення повної таблиці

1. **Створіть базу Airtable** з назвою "TikTok Ads Data"
2. **Імпортуйте CSV** (копіюйте таблицю вище, збережіть як .csv)
3. **Замініть поля з формулами**:
   - Знайдіть поля з "ФОРМУЛА" в описі
   - Змініть тип на "Formula"
   - Вставте відповідну формулу з розділу вище
4. **Налаштуйте Single Select поля** з правильними опціями
5. **Налаштуйте Currency поля** на USD з потрібною кількістю десяткових
6. **Перевірте Duration поля** на формат h:mm:ss

**💡 Порада**: Створюйте поля поступово, спочатку основні, потім додаткові, в кінці формули.

**✅ Результат**: Повнофункціональна Airtable база з автоматичними розрахунками та аналітикою!