# 🔧 Усунення проблем - TikTok Ads Extractor

## ❌ ПРОБЛЕМА: Не видно кнопок витягування даних

### Симптоми
- В popup відображаються тільки налаштування Airtable
- Немає кнопок "📊 Витягти дані з поточної сторінки" та "📤 Відправити в Airtable"
- Інтерфейс обрізаний або неповний

### Причини та рішення

#### 1. Перезавантажте розширення (найчастіше допомагає)
```
1. Перейдіть до chrome://extensions/
2. Знайдіть "TikTok Ads Extractor v3.0.3"
3. Натисніть кнопку "Reload" (🔄) або toggle Off/On
4. Спробуйте відкрити popup знову
```

#### 2. Перевірте консоль на помилки
```
1. Відкрийте popup розширення
2. Натисніть F12 (Developer Tools)
3. Перейдіть на вкладку Console
4. Шукайте помилки JavaScript (червоний текст)
5. Повинні бути логи: "✅ popup.js loaded successfully"
```

#### 3. Очистіть кеш браузера
```
1. Натисніть Ctrl+Shift+Delete
2. Виберіть "Cached images and files"
3. Натисніть "Clear data"
4. Перезавантажте розширення
```

#### 4. Переустановіть розширення
```
1. chrome://extensions/ → видаліть розширення
2. Перезавантажте браузер
3. Встановіть розширення знову (Load unpacked)
4. Перевірте що всі файли на місці
```

## ❌ ПРОБЛЕМА: Розширення не завантажується

### Симптоми
- Помилка "Manifest version not supported"
- Розширення не з'являється в списку
- Помилки при завантаженні

### Рішення
```
1. Використовуйте Chrome 88+ або Chromium-based браузер
2. Увімкніть "Developer mode" в chrome://extensions/
3. Перевірте що всі файли розширення присутні
4. Перевірте права доступу до папки
```

## ❌ ПРОБЛЕМА: Дані не витягуються

### Симптоми
- Кнопка "Витягти дані" не працює
- Повідомлення "No data found"
- Порожній попередній перегляд

### Рішення
```
1. Переконайтеся що ви на ads.tiktok.com
2. Зачекайте повного завантаження сторінки (5-10 секунд)
3. Перейдіть до сторінки з таблицями даних (кампанії/групи/оголошення)
4. Спробуйте оновити сторінку (Ctrl+F5)
5. Перевірте консоль на помилки
```

## ❌ ПРОБЛЕМА: Помилки Airtable

### Симптоми
- "API Key invalid"
- "Base not found"
- "Table not found"
- "Rate limit exceeded"

### Рішення
```
1. Перевірте правильність API ключа (airtable.com/create/tokens)
2. Переконайтеся що Base ID правильний (appXXXXXXXXXXXXXX)
3. Перевірте що Table ID правильний (tblXXXXXXXXXXXXXX)
4. Переконайтеся що у вас є права на запис в таблицю
5. Зачекайте 30 секунд якщо перевищили ліміт запитів
```

## 🔍 Діагностика

### Перевірка стану розширення
```javascript
// Вставте в консоль popup (F12):
console.log('Extension state check:');
console.log('- Extract button:', !!document.getElementById('extractData'));
console.log('- Send button:', !!document.getElementById('sendToAirtable'));
console.log('- Actions div:', !!document.querySelector('.actions'));
console.log('- Total elements:', document.querySelectorAll('*').length);
```

### Перевірка JavaScript
```javascript
// Вставте в консоль popup:
try {
  localStorage.setItem('test', 'ok');
  console.log('✅ localStorage працює');
  localStorage.removeItem('test');
} catch(e) {
  console.log('❌ localStorage заблокований:', e.message);
}
```

### Перевірка TikTok сторінки
```javascript
// Вставте в консоль TikTok Ads сторінки:
console.log('TikTok page check:');
console.log('- Tables found:', document.querySelectorAll('table').length);
console.log('- Rows found:', document.querySelectorAll('tr').length);
console.log('- Campaign elements:', document.querySelectorAll('[data-testid*="campaign"]').length);
```

## 📞 Якщо нічого не допомагає

1. **Перевірте версію браузера** - потрібен Chrome 88+
2. **Спробуйте інший профіль браузера**
3. **Перевірте антивірус** - може блокувати розширення
4. **Спробуйте в режимі інкогніто** (з увімкненими розширеннями)
5. **Створіть issue в репозиторії** з детальним описом проблеми

## ✅ Успішна робота

Якщо все працює правильно, ви повинні бачити:
- Повний інтерфейс з налаштуваннями Airtable
- Кнопки "📊 Витягти дані" та "📤 Відправити в Airtable"
- Розділ "📋 Попередній перегляд даних"
- Статуси операцій з іконками
- Логи в консолі без помилок
