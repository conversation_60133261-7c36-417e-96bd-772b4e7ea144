# TikTok Ads Data Extractor - Cursor Rules

## Загальні принципи розробки

### Мова коментарів та документації
- Використовуйте українську мову для коментарів у коді
- Документація та README файли - українською мовою
- Назви змінних та функцій - англійською мовою
- Повідомлення для користувача - українською мовою

### Архітектура проекту
- **manifest.json** - конфігурація Chrome розширення (Manifest V3)
- **popup.html/js** - інтерфейс користувача (popup при кліку на іконку)
- **content.js** - скрипт для витягування даних з TikTok Ads Manager
- **background.js** - фоновий сервіс для обробки API запитів
- **injected.js** - інжектований скрипт для доступу до React компонентів
- **styles.css** - стилі для popup інтерфейсу

### Стандарти коду

#### JavaScript
- Використовуйте ES6+ синтаксис
- Класи для організації коду
- async/await для асинхронних операцій
- Детальне логування для відладки
- Обробка помилок з try/catch блоками

#### Селектори DOM
- Використовуйте множинні селектори для надійності
- Підтримка data-testid, class, aria-label атрибутів
- Fallback механізми для різних версій TikTok
- Універсальні селектори для таблиць та рядків

#### API інтеграція
- Airtable REST API v0
- Батчування запитів (максимум 10 записів за раз)
- Retry логіка при помилках
- Валідація даних перед відправкою

### Безпека та приватність
- Мінімальні permissions у manifest.json
- Локальне збереження API ключів
- Робота тільки на ads.tiktok.com домені
- Блокування автоматичного відкриття popup як окремої вкладки

### Сумісність
- Chrome 88+ та всі Chromium-based браузери
- Антидетект браузери (Browser.Vision, Octo Browser, GoLogin)
- Підтримка різних операційних систем

### Витягування даних

#### Типи даних
- **Campaigns** - кампанії з бюджетами та показниками
- **AdGroups** - групи оголошень з таргетингом
- **Ads** - окремі оголошення з креативами
- **Performance** - загальні дані продуктивності

#### Метрики для витягування
- Основні: spend, impressions, clicks, CTR, CPC, CPM
- Конверсії: conversions, conversion rate, cost per conversion
- Відео: video views, 2s/6s/25%/50%/75%/100% views
- TikTok-специфічні: likes, shares, comments, follows
- Додаткові: ROAS, reach, frequency, engagement rate

### Обробка помилок

#### Типові проблеми
- Зміна селекторів TikTok (використовуйте fallback)
- API ліміти Airtable (батчування та затримки)
- Блокування JavaScript в антидетект браузерах
- Неправильні назви полів Airtable (case-sensitive)
- Неповне завантаження popup інтерфейсу
- Кешування старих версій файлів

#### Логування
- Детальні логи в console для відладки
- Статуси операцій для користувача
- Помилки з описом та рекомендаціями
- Перевірка наявності DOM елементів
- Версіонування файлів для cache-busting

#### Усунення проблем UI
- Перевірка завантаження popup.js
- Валідація наявності кнопок витягування
- Fallback ініціалізація для різних станів DOM
- Детальне логування процесу завантаження

### UI/UX принципи
- Мінімалістичний та зрозумілий інтерфейс
- Автоматичне заповнення полів з URL
- Миттєвий тест підключення
- Попередній перегляд даних перед відправкою
- Статуси операцій з іконками

### Тестування
- Тестування на різних сторінках TikTok Ads
- Перевірка роботи в антидетект браузерах
- Валідація API підключення
- Тестування з різними обсягами даних

### Версіонування
- Семантичне версіонування (major.minor.patch)
- Оновлення версії в manifest.json та всіх файлах
- Документування змін у README.md

### Розширення функціональності

#### При додаванні нових селекторів
```javascript
// Додавайте в масив selectors у content.js
const newSelectors = [
  '[data-new-testid]',
  '.new-class-name',
  '[aria-label*="new-label"]'
];
```

#### При додаванні нових полів Airtable
```javascript
// Оновлюйте formatDataForAirtable у background.js
fields['New Field'] = item.newValue || '';
```

#### При додаванні нових типів даних
```javascript
// Додавайте в extractAllData у content.js
const newData = this.extractNewDataType();
data.push(...newData);
```

### Документація
- Детальний README.md з інструкціями
- Покрокові гайди для початківців
- Вирішення типових проблем
- Приклади налаштувань для різних браузерів

### Підтримка
- Чіткі повідомлення про помилки
- Посилання на документацію
- Контактна інформація для звітів про баги
- FAQ для типових питань

## Специфічні правила для файлів

### popup.js
- Клас TikTokAdsExtractor для організації коду
- Методи для кожної основної функції
- Валідація введених даних
- Автоматичне парсування Airtable URL

### content.js
- Клас TikTokAdsContentScript
- Множинні селектори для кожного типу даних
- Fallback механізми витягування
- Очищення та валідація даних

### background.js
- Клас TikTokAdsBackgroundService
- Кешування даних для продуктивності
- API інтеграція з обробкою помилок
- Експорт даних у різних форматах

### styles.css
- Responsive дизайн
- Темна/світла тема
- Анімації для кращого UX
- Сумісність з різними розмірами екранів
