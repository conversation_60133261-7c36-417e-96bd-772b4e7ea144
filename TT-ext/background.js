class TikTokAdsBackgroundService {
  constructor() {
    this.cachedData = new Map();
    this.settings = {};
    this.init();
  }

  init() {
    console.log('TikTok Ads Background Service initialized');

    // БЛОКУЄМО БУДЬ-ЯКЕ АВТОМАТИЧНЕ ВІДКРИТТЯ ВКЛАДОК
    this.blockAutoTabOpening();

    // Слухаємо повідомлення від content script і popup
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Асинхронний відповідь
    });

    // Слухаємо встановлення розширення
    chrome.runtime.onInstalled.addListener((details) => {
      this.handleInstall(details);
    });

    // Слухаємо зміни у вкладках
    chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
      this.handleTabUpdate(tabId, changeInfo, tab);
    });

    // Слухаємо зміни в storage
    chrome.storage.onChanged.addListener((changes, namespace) => {
      this.handleStorageChange(changes, namespace);
    });

    // Ініціалізуємо налаштування
    this.loadSettings();
  }

  blockAutoTabOpening() {
    // Запобігаємо автоматичному відкриттю popup.html як окремої вкладки
    const originalTabsCreate = chrome.tabs.create;
    chrome.tabs.create = function(createProperties, callback) {
      if (createProperties.url && createProperties.url.includes('popup.html')) {
        console.log('INFO: Заблоковано спробу відкрити popup.html як вкладку');
        // Просто ігноруємо запит, не викликаємо callback для уникнення помилок
        return;
      }
      return originalTabsCreate.call(this, createProperties, callback);
    };

    // Запобігаємо навігації до popup.html в існуючих вкладках
    const originalTabsUpdate = chrome.tabs.update;
    chrome.tabs.update = function(tabId, updateProperties, callback) {
      if (updateProperties.url && updateProperties.url.includes('popup.html')) {
        console.log('INFO: Заблоковано спробу навігації до popup.html');
        return;
      }
      return originalTabsUpdate.call(this, tabId, updateProperties, callback);
    };
  }

  async handleMessage(request, sender, sendResponse) {
    try {
      switch (request.action) {
        case 'cacheData':
          await this.cacheData(request.data, request.timestamp, sender.tab);
          sendResponse({ success: true });
          break;

        case 'getCachedData':
          const cachedData = this.getCachedData(request.tabId || sender.tab?.id);
          sendResponse({ success: true, data: cachedData });
          break;

        case 'clearCache':
          this.clearCache(request.tabId);
          sendResponse({ success: true });
          break;

        case 'sendToAirtable':
          const result = await this.sendToAirtable(request.data, request.settings);
          sendResponse(result);
          break;

        case 'getSettings':
          sendResponse({ success: true, settings: this.settings });
          break;

        case 'validateAirtableConnection':
          const validation = await this.validateAirtableConnection(request.settings);
          sendResponse(validation);
          break;

        case 'exportData':
          const exportResult = await this.exportData(request.data, request.format);
          sendResponse(exportResult);
          break;

        default:
          sendResponse({ success: false, error: 'Unknown action' });
      }
    } catch (error) {
      console.error('Background script error:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async handleInstall(details) {
    console.log('Extension installed:', details);

    if (details.reason === 'install') {
      // Перша установка - тільки налаштування за замовчуванням
      await this.setDefaultSettings();
      console.log('Extension installed successfully - use popup to access functionality');
    } else if (details.reason === 'update') {
      // Оновлення розширення
      console.log('Extension updated from', details.previousVersion);
    }
  }

  handleTabUpdate(tabId, changeInfo, tab) {
    if (changeInfo.status === 'complete' && tab.url?.includes('ads.tiktok.com')) {
      console.log('TikTok Ads page loaded:', tab.url);
      
      // Очищуємо старий кеш для цієї вкладки
      this.clearCache(tabId);
      
      // Повідомляємо content script про готовність
      chrome.tabs.sendMessage(tabId, { action: 'pageReady' }).catch(() => {
        // Ігноруємо помилки, якщо content script ще не готовий
      });
    }
  }

  handleStorageChange(changes, namespace) {
    if (namespace === 'sync') {
      // Оновлюємо локальні налаштування при змінах
      console.log('Storage changed:', Object.keys(changes));
      this.loadSettings();
    }
  }

  async loadSettings() {
    try {
      const stored = await chrome.storage.sync.get(['apiKey', 'baseId', 'tableId', 'autoExtract', 'exportFormat']);
      this.settings = {
        apiKey: stored.apiKey || '',
        baseId: stored.baseId || '',
        tableId: stored.tableId || '',
        autoExtract: stored.autoExtract || false,
        exportFormat: stored.exportFormat || 'json',
        ...stored
      };
    } catch (error) {
      console.error('Error loading settings:', error);
    }
  }

  async setDefaultSettings() {
    const defaultSettings = {
      autoExtract: true,
      exportFormat: 'json',
      maxCacheSize: 1000,
      cacheExpiry: 24 * 60 * 60 * 1000 // 24 години
    };

    await chrome.storage.sync.set(defaultSettings);
    this.settings = { ...this.settings, ...defaultSettings };
  }

  async cacheData(data, timestamp, tab) {
    if (!data || !Array.isArray(data) || data.length === 0) return;

    const tabId = tab?.id;
    if (!tabId) return;

    const cacheEntry = {
      data: data,
      timestamp: timestamp || Date.now(),
      url: tab.url,
      title: tab.title,
      count: data.length
    };

    this.cachedData.set(tabId, cacheEntry);
    
    // Зберігаємо в local storage для персистентності
    try {
      await chrome.storage.local.set({
        [`cache_${tabId}`]: cacheEntry
      });
    } catch (error) {
      console.warn('Error saving cache to storage:', error);
    }

    console.log(`Cached ${data.length} items for tab ${tabId}`);
    
    // Обмежуємо розмір кешу
    this.limitCacheSize();
  }

  getCachedData(tabId) {
    if (!tabId) return null;
    
    const cached = this.cachedData.get(tabId);
    
    if (cached) {
      // Перевіряємо, чи не застарів кеш
      const age = Date.now() - cached.timestamp;
      const maxAge = this.settings.cacheExpiry || (24 * 60 * 60 * 1000);
      
      if (age > maxAge) {
        this.clearCache(tabId);
        return null;
      }
    }
    
    return cached;
  }

  clearCache(tabId) {
    if (tabId) {
      this.cachedData.delete(tabId);
      chrome.storage.local.remove(`cache_${tabId}`).catch(() => {});
    } else {
      // Очищуємо весь кеш
      this.cachedData.clear();
      chrome.storage.local.clear().catch(() => {});
    }
  }

  limitCacheSize() {
    const maxSize = this.settings.maxCacheSize || 1000;
    
    if (this.cachedData.size > maxSize) {
      // Видаляємо найстаріші записи
      const entries = Array.from(this.cachedData.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toRemove = entries.slice(0, entries.length - maxSize);
      toRemove.forEach(([tabId]) => {
        this.clearCache(tabId);
      });
    }
  }

  async sendToAirtable(data, settings) {
    if (!data || !Array.isArray(data) || data.length === 0) {
      return { success: false, error: 'Немає даних для відправки' };
    }

    if (!settings?.apiKey || !settings?.baseId || !settings?.tableId) {
      return { success: false, error: 'Не налаштовано підключення до Airtable' };
    }

    try {
      // Підготовляємо записи для Airtable
      const records = data.map(item => ({
        fields: this.formatDataForAirtable(item)
      }));

      // Розбиваємо на батчі по 10 записів (обмеження Airtable)
      const batches = this.chunkArray(records, 10);
      const results = [];

      for (const batch of batches) {
        const response = await fetch(`https://api.airtable.com/v0/${settings.baseId}/${settings.tableId}`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${settings.apiKey}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ records: batch })
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.error?.message || 'Помилка API Airtable');
        }

        const result = await response.json();
        results.push(...result.records);
        
        // Невелика затримка між батчами
        if (batches.length > 1) {
          await new Promise(resolve => setTimeout(resolve, 200));
        }
      }

      return { 
        success: true, 
        records: results,
        message: `Успішно відправлено ${results.length} записів`
      };
      
    } catch (error) {
      console.error('Airtable send error:', error);
      return { success: false, error: error.message };
    }
  }

  formatDataForAirtable(item) {
    const fields = {
      'ID': item.id || '',
      'Type': item.type || '',
      'Name': item.name || '',
      'Status': item.status || '',
      'URL': item.url || '',
      'Extracted At': item.extractedAt || new Date().toISOString(),
      'Source': item.source || 'extension'
    };

    // Додаємо специфічні поля в залежності від типу
    switch (item.type) {
      case 'campaign':
        Object.assign(fields, {
          'Objective': item.objective || '',
          'Budget': item.budget || '',
          'Spend': item.spend || '',
          'Impressions': item.impressions || '',
          'Clicks': item.clicks || '',
          'CTR': item.ctr || '',
          'CPC': item.cpc || '',
          'CPM': item.cpm || '',
          'Conversions': item.conversions || '',
          'Conversion Rate': item.conversionRate || '',
          'Cost per Conversion': item.costPerConversion || '',
          'ROAS': item.roas || '',
          'Reach': item.reach || '',
          'Frequency': item.frequency || '',

          // Video metrics
          'Video Views': item.videoViews || '',
          'Video 2s Views': item.video2sViews || '',
          'Video 6s Views': item.video6sViews || '',
          'Video 25% Views': item.video25Views || '',
          'Video 50% Views': item.video50Views || '',
          'Video 75% Views': item.video75Views || '',
          'Video 100% Views': item.video100Views || '',
          'Video Play Actions': item.videoPlayActions || '',
          'Video Watch Time': item.videoWatchTime || '',
          'Avg Video Play Time': item.avgVideoPlayTime || '',
          'Video Completion Rate': item.videoCompletionRate || '',

          // TikTok-specific metrics
          'Likes': item.likes || '',
          'Shares': item.shares || '',
          'Comments': item.comments || '',
          'Follows': item.follows || '',
          'Profile Views': item.profileViews || '',
          'Engagement Rate': item.engagementRate || '',
          'Social Engagement': item.socialEngagement || '',
          'Unique Users': item.uniqueUsers || '',
          'New Users': item.newUsers || ''
        });
        break;
      
      case 'adgroup':
        Object.assign(fields, {
          'Placement': item.placement || '',
          'Audience': item.audience || '',
          'Budget': item.budget || '',
          'Bidding': item.bidding || '',
          'Spend': item.spend || '',
          'Impressions': item.impressions || '',
          'Clicks': item.clicks || '',
          'CTR': item.ctr || '',
          'CPC': item.cpc || '',
          'CPM': item.cpm || '',
          'Conversions': item.conversions || '',
          'Conversion Rate': item.conversionRate || '',
          'Cost per Conversion': item.costPerConversion || '',
          'ROAS': item.roas || '',
          'Reach': item.reach || '',
          'Frequency': item.frequency || '',

          // Video metrics для AdGroups
          'Video Views': item.videoViews || '',
          'Video 2s Views': item.video2sViews || '',
          'Video 6s Views': item.video6sViews || '',
          'Video 25% Views': item.video25Views || '',
          'Video 50% Views': item.video50Views || '',
          'Video 75% Views': item.video75Views || '',
          'Video 100% Views': item.video100Views || '',
          'Video Play Actions': item.videoPlayActions || '',
          'Video Watch Time': item.videoWatchTime || '',
          'Avg Video Play Time': item.avgVideoPlayTime || '',

          // TikTok-specific metrics для AdGroups
          'Likes': item.likes || '',
          'Shares': item.shares || '',
          'Comments': item.comments || '',
          'Follows': item.follows || '',
          'Engagement Rate': item.engagementRate || ''
        });
        break;
      
      case 'ad':
        Object.assign(fields, {
          'Format': item.format || '',
          'Spend': item.spend || '',
          'Impressions': item.impressions || '',
          'Clicks': item.clicks || '',
          'CTR': item.ctr || '',
          'CPC': item.cpc || '',
          'CPM': item.cpm || '',
          'Conversions': item.conversions || '',
          'Conversion Rate': item.conversionRate || '',
          'Cost per Conversion': item.costPerConversion || '',
          'ROAS': item.roas || '',
          'Reach': item.reach || '',
          'Frequency': item.frequency || '',

          // Video metrics для Ads
          'Video Views': item.videoViews || '',
          'Video 2s Views': item.video2sViews || '',
          'Video 6s Views': item.video6sViews || '',
          'Video 25% Views': item.video25Views || '',
          'Video 50% Views': item.video50Views || '',
          'Video 75% Views': item.video75Views || '',
          'Video 100% Views': item.video100Views || '',
          'Video Play Actions': item.videoPlayActions || '',
          'Video Watch Time': item.videoWatchTime || '',
          'Avg Video Play Time': item.avgVideoPlayTime || '',
          'Video Completion Rate': item.videoCompletionRate || '',

          // TikTok-specific metrics для Ads
          'Likes': item.likes || '',
          'Shares': item.shares || '',
          'Comments': item.comments || '',
          'Follows': item.follows || '',
          'Profile Views': item.profileViews || '',
          'Engagement Rate': item.engagementRate || '',
          'Creative Views': item.creativeViews || '',
          'Ad Preview': item.adPreview || ''
        });
        break;
      
      default:
        if (item.data && typeof item.data === 'object') {
          fields['Raw Data'] = JSON.stringify(item.data);
        }
    }

    // Видаляємо поля з порожніми значеннями
    Object.keys(fields).forEach(key => {
      if (!fields[key] || fields[key] === '') {
        delete fields[key];
      }
    });

    return fields;
  }

  async validateAirtableConnection(settings) {
    if (!settings?.apiKey || !settings?.baseId || !settings?.tableId) {
      return { success: false, error: 'Не всі поля заповнено' };
    }

    try {
      const response = await fetch(`https://api.airtable.com/v0/${settings.baseId}/${settings.tableId}?maxRecords=1`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${settings.apiKey}`
        }
      });

      if (!response.ok) {
        const error = await response.json();
        return { success: false, error: error.error?.message || 'Невірні дані доступу' };
      }

      return { success: true, message: 'Підключення до Airtable успішне' };
      
    } catch (error) {
      return { success: false, error: 'Помилка мережі або невірні дані' };
    }
  }

  async exportData(data, format = 'json') {
    if (!data || !Array.isArray(data)) {
      return { success: false, error: 'Немає даних для експорту' };
    }

    try {
      let content, mimeType, filename;

      switch (format.toLowerCase()) {
        case 'csv':
          content = this.convertToCSV(data);
          mimeType = 'text/csv';
          filename = `tiktok_ads_data_${Date.now()}.csv`;
          break;
        
        case 'json':
        default:
          content = JSON.stringify(data, null, 2);
          mimeType = 'application/json';
          filename = `tiktok_ads_data_${Date.now()}.json`;
          break;
      }

      // Створюємо URL для завантаження
      const blob = new Blob([content], { type: mimeType });
      const url = URL.createObjectURL(blob);

      // Завантажуємо файл
      await chrome.downloads.download({
        url: url,
        filename: filename,
        saveAs: true
      });

      return { success: true, filename };
      
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  convertToCSV(data) {
    if (!data.length) return '';

    // Збираємо всі можливі ключі
    const allKeys = new Set();
    data.forEach(item => {
      Object.keys(item).forEach(key => allKeys.add(key));
      if (item.data && typeof item.data === 'object') {
        Object.keys(item.data).forEach(key => allKeys.add(`data.${key}`));
      }
    });

    const headers = Array.from(allKeys);
    
    // Створюємо CSV
    const csvRows = [headers.join(',')];
    
    data.forEach(item => {
      const row = headers.map(header => {
        let value;
        
        if (header.startsWith('data.')) {
          const dataKey = header.substring(5);
          value = item.data?.[dataKey] || '';
        } else {
          value = item[header] || '';
        }
        
        // Екрануємо спеціальні символи
        if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
          value = `"${value.replace(/"/g, '""')}"`;
        }
        
        return value;
      });
      
      csvRows.push(row.join(','));
    });

    return csvRows.join('\n');
  }

  chunkArray(array, size) {
    const chunks = [];
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size));
    }
    return chunks;
  }
}

// Ініціалізуємо background service
new TikTokAdsBackgroundService();